# 用户认证模块规范

## 📋 模块概述

用户认证模块负责处理用户的注册、登录、身份验证和权限管理，是整个系统的安全基础。

### 核心功能
- 多种登录方式（一键登录、密码登录、生物识别）
- JWT令牌管理和刷新机制
- 多设备管理和安全控制
- 用户资料管理和配置

### 技术要求
- **安全性**: JWT + bcrypt，支持生物识别
- **可用性**: 离线登录状态保持，自动令牌刷新
- **性能**: 登录响应时间 < 2秒，令牌验证 < 100ms
- **兼容性**: 支持iOS/Android生物识别，Web端密码登录

## 🔧 技术实现规范

### 前端实现 (Flutter)

#### 目录结构
```
lib/features/auth/
├── data/
│   ├── datasources/
│   │   ├── auth_local_datasource.dart    # 本地数据源
│   │   └── auth_remote_datasource.dart   # 远程数据源
│   ├── models/
│   │   ├── user_model.dart               # 用户数据模型
│   │   ├── auth_request_model.dart       # 认证请求模型
│   │   └── auth_response_model.dart      # 认证响应模型
│   └── repositories/
│       └── auth_repository_impl.dart     # 仓储实现
├── domain/
│   ├── entities/
│   │   └── user_entity.dart              # 用户实体
│   ├── repositories/
│   │   └── auth_repository.dart          # 仓储接口
│   └── usecases/
│       ├── login_user.dart               # 登录用例
│       ├── logout_user.dart              # 登出用例
│       ├── refresh_token.dart            # 刷新令牌用例
│       └── get_current_user.dart         # 获取当前用户用例
└── presentation/
    ├── controllers/
    │   └── auth_controller.dart          # 认证控制器
    ├── pages/
    │   ├── login_page.dart               # 登录页面
    │   └── profile_page.dart             # 个人资料页面
    └── widgets/
        ├── login_form.dart               # 登录表单
        ├── biometric_button.dart         # 生物识别按钮
        └── auth_status_indicator.dart    # 认证状态指示器
```

#### 核心类定义

```dart
// domain/entities/user_entity.dart
class UserEntity {
  final String id;
  final String mobile;
  final String? username;
  final String? email;
  final String? avatar;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final bool isActive;
  
  const UserEntity({
    required this.id,
    required this.mobile,
    this.username,
    this.email,
    this.avatar,
    required this.createdAt,
    this.lastLoginAt,
    required this.isActive,
  });
}

// domain/repositories/auth_repository.dart
abstract class AuthRepository {
  Future<Either<Failure, AuthResponse>> login(String mobile, String smsCode);
  Future<Either<Failure, AuthResponse>> loginWithPassword(String username, String password);
  Future<Either<Failure, AuthResponse>> loginWithBiometric(String biometricToken);
  Future<Either<Failure, AuthResponse>> refreshToken(String refreshToken);
  Future<Either<Failure, void>> logout();
  Future<Either<Failure, UserEntity>> getCurrentUser();
  Future<Either<Failure, void>> updateProfile(UserEntity user);
}

// presentation/controllers/auth_controller.dart
class AuthController extends GetxController {
  final LoginUser _loginUser;
  final LogoutUser _logoutUser;
  final GetCurrentUser _getCurrentUser;
  final RefreshToken _refreshToken;
  
  // 响应式状态
  final _isLoading = false.obs;
  final _user = Rxn<UserEntity>();
  final _authStatus = AuthStatus.initial.obs;
  
  // Getters
  bool get isLoading => _isLoading.value;
  UserEntity? get user => _user.value;
  AuthStatus get authStatus => _authStatus.value;
  bool get isAuthenticated => _authStatus.value == AuthStatus.authenticated;
  
  @override
  void onInit() {
    super.onInit();
    _checkAuthStatus();
    _setupTokenRefresh();
  }
  
  Future<void> login(String mobile, String smsCode) async {
    _isLoading.value = true;
    try {
      final result = await _loginUser(LoginParams(mobile: mobile, smsCode: smsCode));
      result.fold(
        (failure) => _handleAuthFailure(failure),
        (authResponse) => _handleAuthSuccess(authResponse),
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<void> loginWithBiometric() async {
    try {
      final biometricToken = await _getBiometricToken();
      if (biometricToken != null) {
        await login(biometricToken, '');
      }
    } catch (e) {
      _handleAuthError('生物识别登录失败: $e');
    }
  }
  
  void _handleAuthSuccess(AuthResponse response) {
    _user.value = response.user;
    _authStatus.value = AuthStatus.authenticated;
    _saveTokens(response.accessToken, response.refreshToken);
    Get.offAllNamed(Routes.HOME);
  }
  
  void _handleAuthFailure(Failure failure) {
    _authStatus.value = AuthStatus.unauthenticated;
    Get.snackbar('登录失败', failure.message);
  }
}
```

### 后端实现 (FastAPI)

#### 目录结构
```
apps/auth/
├── models.py          # Tortoise ORM模型
├── schemas.py         # Pydantic数据模式
├── services.py        # 业务逻辑服务
├── routers.py         # FastAPI路由
├── dependencies.py    # 依赖注入
└── exceptions.py      # 自定义异常
```

#### 核心类定义

```python
# models.py
from tortoise.models import Model
from tortoise import fields
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class User(Model):
    id = fields.UUIDField(pk=True)
    mobile = fields.CharField(max_length=20, unique=True)
    username = fields.CharField(max_length=50, null=True)
    email = fields.CharField(max_length=100, null=True)
    password_hash = fields.CharField(max_length=255, null=True)
    avatar = fields.CharField(max_length=500, null=True)
    is_active = fields.BooleanField(default=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    last_login_at = fields.DatetimeField(null=True)
    
    class Meta:
        table = "users"
    
    def verify_password(self, password: str) -> bool:
        return pwd_context.verify(password, self.password_hash)
    
    def set_password(self, password: str):
        self.password_hash = pwd_context.hash(password)

class Device(Model):
    id = fields.UUIDField(pk=True)
    user = fields.ForeignKeyField("models.User", related_name="devices")
    device_name = fields.CharField(max_length=100)
    device_type = fields.CharField(max_length=20)  # ios, android, web
    device_token = fields.CharField(max_length=500, null=True)
    is_active = fields.BooleanField(default=True)
    last_active = fields.DatetimeField(auto_now=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    
    class Meta:
        table = "devices"

# schemas.py
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class LoginRequest(BaseModel):
    mobile: str = Field(..., regex=r"^1[3-9]\d{9}$")
    sms_code: str = Field(..., min_length=6, max_length=6)

class LoginWithPasswordRequest(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=8, max_length=50)

class AuthResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: "UserResponse"

class UserResponse(BaseModel):
    id: str
    mobile: str
    username: Optional[str]
    email: Optional[str]
    avatar: Optional[str]
    created_at: datetime
    last_login_at: Optional[datetime]
    
    class Config:
        from_attributes = True

# services.py
from datetime import datetime, timedelta
from jose import JWTError, jwt
from fastapi import HTTPException, status

class AuthService:
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = 15
        self.refresh_token_expire_days = 7
    
    async def authenticate_user(self, mobile: str, sms_code: str) -> Optional[User]:
        # 验证短信验证码
        if not await self._verify_sms_code(mobile, sms_code):
            return None
        
        # 获取或创建用户
        user, created = await User.get_or_create(mobile=mobile)
        if created:
            user.username = f"用户{mobile[-4:]}"
            await user.save()
        
        # 更新最后登录时间
        user.last_login_at = datetime.utcnow()
        await user.save()
        
        return user
    
    async def authenticate_with_password(self, username: str, password: str) -> Optional[User]:
        user = await User.filter(
            Q(username=username) | Q(mobile=username) | Q(email=username)
        ).first()
        
        if user and user.verify_password(password):
            user.last_login_at = datetime.utcnow()
            await user.save()
            return user
        return None
    
    def create_access_token(self, user_id: str, device_id: str) -> str:
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode = {
            "sub": user_id,
            "device_id": device_id,
            "exp": expire,
            "type": "access"
        }
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, user_id: str, device_id: str) -> str:
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        to_encode = {
            "sub": user_id,
            "device_id": device_id,
            "exp": expire,
            "type": "refresh"
        }
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> dict:
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )

# routers.py
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

router = APIRouter(prefix="/auth", tags=["认证"])
security = HTTPBearer()

@router.post("/login", response_model=AuthResponse)
async def login(
    request: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    user = await auth_service.authenticate_user(request.mobile, request.sms_code)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="手机号或验证码错误"
        )
    
    # 注册或更新设备信息
    device = await register_device(user.id, request.device_info)
    
    # 生成令牌
    access_token = auth_service.create_access_token(str(user.id), str(device.id))
    refresh_token = auth_service.create_refresh_token(str(user.id), str(device.id))
    
    return AuthResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=auth_service.access_token_expire_minutes * 60,
        user=UserResponse.from_orm(user)
    )

@router.post("/refresh", response_model=AuthResponse)
async def refresh_token(
    refresh_token: str,
    auth_service: AuthService = Depends(get_auth_service)
):
    payload = auth_service.verify_token(refresh_token)
    if payload.get("type") != "refresh":
        raise HTTPException(status_code=401, detail="无效的刷新令牌")
    
    user_id = payload.get("sub")
    device_id = payload.get("device_id")
    
    # 验证用户和设备是否仍然有效
    user = await User.get_or_none(id=user_id, is_active=True)
    device = await Device.get_or_none(id=device_id, is_active=True)
    
    if not user or not device:
        raise HTTPException(status_code=401, detail="用户或设备无效")
    
    # 生成新的令牌
    new_access_token = auth_service.create_access_token(user_id, device_id)
    new_refresh_token = auth_service.create_refresh_token(user_id, device_id)
    
    return AuthResponse(
        access_token=new_access_token,
        refresh_token=new_refresh_token,
        expires_in=auth_service.access_token_expire_minutes * 60,
        user=UserResponse.from_orm(user)
    )

@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user: User = Depends(get_current_user)
):
    return UserResponse.from_orm(current_user)

@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
):
    payload = auth_service.verify_token(credentials.credentials)
    device_id = payload.get("device_id")
    
    # 停用设备
    await Device.filter(id=device_id).update(is_active=False)
    
    return {"message": "登出成功"}
```

## 📊 数据模型设计

### 本地数据库表结构 (SQLite)
```sql
-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    mobile TEXT UNIQUE NOT NULL,
    username TEXT,
    email TEXT,
    avatar TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    last_login_at DATETIME,
    is_active BOOLEAN DEFAULT 1
);

-- 用户配置表
CREATE TABLE user_configs (
    user_id TEXT PRIMARY KEY,
    theme TEXT DEFAULT 'system',
    language TEXT DEFAULT 'zh-CN',
    biometric_enabled BOOLEAN DEFAULT 0,
    auto_sync BOOLEAN DEFAULT 1,
    notification_enabled BOOLEAN DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 认证令牌表（本地缓存）
CREATE TABLE auth_tokens (
    user_id TEXT PRIMARY KEY,
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    expires_at DATETIME NOT NULL,
    device_id TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 云端数据库表结构 (PostgreSQL)
```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mobile VARCHAR(20) UNIQUE NOT NULL,
    username VARCHAR(50),
    email VARCHAR(100),
    password_hash VARCHAR(255),
    avatar VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- 设备表
CREATE TABLE devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_name VARCHAR(100) NOT NULL,
    device_type VARCHAR(20) NOT NULL, -- ios, android, web
    device_token VARCHAR(500),
    device_fingerprint VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 短信验证码表
CREATE TABLE sms_verifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mobile VARCHAR(20) NOT NULL,
    code VARCHAR(6) NOT NULL,
    type VARCHAR(20) NOT NULL, -- login, register, reset
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    attempts INTEGER DEFAULT 0,
    verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_users_mobile ON users(mobile);
CREATE INDEX idx_devices_user_id ON devices(user_id);
CREATE INDEX idx_sms_mobile_type ON sms_verifications(mobile, type);
```

## 🔐 安全规范

### JWT令牌设计
```json
{
  "access_token": {
    "sub": "user_id",
    "device_id": "device_id",
    "exp": 1640995200,
    "type": "access",
    "iat": 1640991600
  },
  "refresh_token": {
    "sub": "user_id", 
    "device_id": "device_id",
    "exp": 1641596400,
    "type": "refresh",
    "iat": 1640991600
  }
}
```

### 安全措施
1. **密码安全**: bcrypt哈希，最少8位包含字母数字
2. **令牌安全**: JWT签名验证，短期访问令牌 + 长期刷新令牌
3. **设备绑定**: 令牌与设备ID绑定，防止令牌盗用
4. **生物识别**: 本地生物识别 + 服务端验证
5. **防暴力破解**: 登录失败次数限制，账户临时锁定

## ✅ 测试规范

### 单元测试覆盖
- AuthController所有方法
- AuthService业务逻辑
- 数据模型验证
- 工具函数测试

### 集成测试场景
- 完整登录流程
- 令牌刷新机制
- 多设备管理
- 异常情况处理

### 性能测试指标
- 登录响应时间 < 2秒
- 令牌验证时间 < 100ms
- 并发登录支持 1000+
- 数据库查询优化

---

**注意**：本规范定义了认证模块的完整技术实现，包括前后端代码结构、数据模型、安全措施等。开发时应严格按照此规范执行。
