# 产品需求文档（PRD）

## 1. 文档信息

| 版本 | 日期       | 作者          | 备注 |
| ---- | ---------- | ------------- | ---- |
| 1.0  | 2025-01-19 | CheeStack团队 | 初版 |

---

## 2. 项目背景

### 2.1 项目概述
- **项目简介**：CheeStack (芝士堆) 是一款基于FSRS算法的智能学习工具，专注于帮助用户高效记忆和掌握各类知识点。通过科学的间隔重复算法，结合多媒体学习内容和智能复习调度，为用户提供个性化的学习体验，解决传统学习方法效率低下、遗忘率高、难以坚持的核心问题。

## 3. 业务需求分析

### 3.1 业务背景
- **现状分析**：
  - 传统学习方法效率低下，学习者普遍存在遗忘率高（24小时内遗忘75%）、学习时间安排不科学、缺乏个性化指导等问题
  - 现有学习工具功能单一，缺乏科学的记忆算法支持，用户学习效果难以量化和提升
  - 移动学习需求旺盛，但市场上缺乏真正基于记忆科学的智能学习工具
- **市场环境**：
  - 在线教育市场规模5000亿+，移动学习占比60%以上
  - 主要竞争对手：Anki、Quizlet、百词斩等，但都存在算法落后、用户体验差等问题
  - 市场机会：FSRS算法的先进性为我们提供了技术优势和差异化竞争力
- **业务驱动因素**：
  - 用户对高效学习工具的强烈需求
  - FSRS算法的技术突破和成熟应用
  - 移动设备性能提升支持复杂算法本地运行
  - 疫情推动在线学习习惯养成

### 3.2 用户分析
#### 3.2.1 目标用户群体
| 用户类型   | 用户特征                           | 核心需求                       | 使用频率       |
| ---------- | ---------------------------------- | ------------------------------ | -------------- |
| 语言学习者 | 18-35岁，学生/白领，有一定英语基础 | 词汇记忆、口语练习、语法学习   | 每天30-60分钟  |
| 学生群体   | 12-25岁，中学生/大学生，学习压力大 | 考试备考、知识点记忆、错题复习 | 每天60-120分钟 |
| 职场人士   | 25-40岁，上班族，时间碎片化        | 技能提升、证书考试、专业知识   | 每天20-40分钟  |
| 知识爱好者 | 25-50岁，终身学习者，求知欲强      | 兴趣学习、知识积累、自我提升   | 每天15-30分钟  |

#### 3.2.2 用户画像
**主要用户画像**：
- **基本信息**：25岁，女性，大学生/初级白领，月收入3000-8000元
- **行为特征**：
  - 学习习惯：喜欢利用碎片时间学习，偏好视觉化和互动式内容
  - 技术接受度：熟练使用智能手机，对新技术接受度高
  - 使用偏好：注重学习效果和效率，愿意为优质工具付费
- **痛点分析**：
  - 学习效率低：传统死记硬背效果差，容易遗忘
  - 时间管理难：不知道什么时候复习，复习安排不科学
  - 缺乏反馈：学习进度和效果难以量化，缺乏成就感
  - 坚持困难：学习枯燥乏味，难以长期坚持
- **期望价值**：
  - 学习效率显著提升，记忆更持久
  - 智能化学习安排，节省时间精力
  - 可视化学习进度，获得成就感
  - 个性化学习体验，保持学习兴趣

### 3.3 业务场景
#### 3.3.1 核心使用场景
1. **场景1：内容创作与管理**
   - **触发条件**：用户需要创建学习内容，整理知识体系
   - **用户目标**：高效创建和组织学习材料，建立个人知识库
   - **操作流程**：创建学习书籍 → 设计章节结构 → 制作学习卡片 → 添加多媒体内容 → 设置标签分类
   - **预期结果**：建立结构化的学习内容，便于后续学习和复习

2. **场景2：日常学习复习**
   - **触发条件**：用户打开应用，系统推荐今日需要复习的卡片
   - **用户目标**：完成当天的学习任务，巩固已学知识
   - **操作流程**：查看复习提醒 → 开始复习 → 评价记忆效果 → 查看学习统计
   - **预期结果**：完成复习任务，记忆得到巩固，获得学习成就感

3. **场景3：新知识学习**
   - **触发条件**：用户需要学习新的知识点或准备考试
   - **用户目标**：高效掌握新知识，建立长期记忆
   - **操作流程**：创建/选择学习书籍 → 学习新卡片 → 初次记忆评价 → 加入复习计划
   - **预期结果**：新知识得到有效记忆，自动安排后续复习

4. **场景4：考试备考冲刺**
   - **触发条件**：临近考试，需要集中复习和查漏补缺
   - **用户目标**：快速复习重点内容，提高考试通过率
   - **操作流程**：选择考试模式 → 重点内容强化 → 错题集复习 → 模拟测试
   - **预期结果**：考试重点得到强化，薄弱环节得到改善

### 3.4 核心业务流程
```mermaid
flowchart TD
    A[用户进入系统] --> B{是否已登录?}
    B -->|否| C[用户注册/登录]
    B -->|是| D[进入主功能]
    C --> D
    D --> E[选择功能模块]
    E --> F{选择学习模式}
    F -->|新学习| G[学习新卡片]
    F -->|复习模式| H[智能复习调度]
    F -->|考试模式| I[模拟考试练习]
    G --> J[FSRS算法计算]
    H --> J
    I --> K[成绩分析]
    J --> L[更新学习记录]
    K --> L
    L --> M[同步到云端]
    M --> N{是否继续?}
    N -->|是| E
    N -->|否| O[查看学习统计]
    O --> P[退出系统]
```

**流程说明**：
1. **用户入口**：支持一键登录、本机号码登录、密码登录等多种方式快速进入
2. **身份验证**：JWT Token认证，支持多设备登录和设备管理
3. **核心操作**：基于FSRS算法的智能学习和复习，支持多种学习模式
4. **结果反馈**：实时学习统计、进度可视化、个性化建议等多维度反馈

---


## 5. 功能需求详细设计

> **AI生成说明**：每个功能模块都必须按照以下结构详细描述，确保开发团队能够直接基于此进行技术设计和开发实施。

### 5.1 用户身份认证模块 - 登录注册系统

**优先级**: Must
**复杂度**: 中等
**预估工期**: 5人天
**依赖模块**: 设备管理系统
**对应代码模块**: `cheestack-flt/lib/features/auth/`

#### 🎯 功能概述
- **功能定义**：提供完整的用户注册、登录和身份验证功能，采用一键登录模式，简化用户操作流程
- **核心价值**：降低用户使用门槛，提升用户体验，确保账户安全和快速身份验证
- **使用场景**：用户首次使用应用时一键登录（自动注册），后续使用支持多种登录方式，快速身份验证
- **业务规则**：
  - 支持手机号一键登录（有账号则登录，无账号则自动注册）
  - 支持本机号码一键登录（自动识别本机号码并登录/注册）
  - 保留传统密码登录方式作为备选方案
  - JWT Token认证机制，Token有效期180天，支持自动续期
  - 账户状态验证（正常、禁用、删除）
  - 密码安全策略：最少8位，包含字母和数字
  - 支持生物识别登录（指纹、面容ID）
- **前置条件**：用户拥有有效的手机号码，设备支持获取本机号码（可选）

#### 🔄 功能流程设计
```mermaid
flowchart TD
    A[用户打开应用] --> B[检查登录状态]
    B --> C{Token是否有效?}
    C -->|是| D[进入主界面]
    C -->|否| E[显示登录页面]
    E --> F{选择登录方式}
    F -->|一键登录| G[输入手机号]
    F -->|本机一键登录| H[自动获取本机号码]
    F -->|密码登录| I[输入用户名/手机号+密码]
    F -->|生物识别| J[生物识别验证]
    G --> K[发送验证码]
    H --> K
    I --> L[验证密码]
    J --> M[验证生物识别]
    K --> N[验证码校验]
    L --> O{用户是否存在?}
    M --> P{生物识别成功?}
    N --> Q{用户是否存在?}
    O -->|是| R[登录成功]
    O -->|否| S[登录失败提示]
    P -->|是| R
    P -->|否| S
    Q -->|是| R
    Q -->|否| T[自动注册新用户]
    T --> U[初始化基础用户信息]
    U --> R
    R --> V[生成JWT Token]
    V --> W[注册设备信息]
    W --> D
    S --> E
```

**流程步骤详解**：
1. **触发阶段**：用户打开应用，系统自动检查本地Token有效性
2. **验证阶段**：
   - Token验证：检查JWT Token是否存在且未过期
   - 权限验证：验证用户账户状态（正常/禁用/删除）
   - 数据验证：手机号格式验证、验证码有效性验证、密码强度验证
   - 生物识别验证：指纹或面容ID验证（如果已启用）
3. **执行阶段**：
   - 一键登录：发送短信验证码 → 用户输入验证码 → 系统验证 → 登录/注册
   - 密码登录：用户输入凭据 → 系统验证密码 → 检查用户状态 → 登录成功
   - 生物识别登录：调用系统生物识别API → 验证成功 → 直接登录
   - 自动注册：新用户通过验证码验证后自动创建基础账户信息
4. **反馈阶段**：
   - 成功：生成JWT Token，注册设备信息，跳转主界面
   - 失败：显示具体错误信息，引导用户重新操作
5. **异常处理**：
   - 网络异常：显示网络错误提示，支持重试
   - 验证码错误：限制重试次数，防止恶意攻击
   - 密码错误：记录失败次数，超过限制后锁定账户
   - Token过期：自动清除本地Token，引导重新登录
   - 生物识别失败：回退到其他登录方式

**边界条件**：
- **正常边界**：手机号11位数字，验证码6位数字，密码8-20位字符
- **异常边界**：验证码5分钟过期，密码错误5次锁定，Token 7天过期
- **性能边界**：登录接口响应时间<2秒，验证码发送间隔60秒

#### 📊 数据需求
**数据结构**：
```json
{
  "user_auth": {
    "id": "string",
    "mobile": "string",
    "username": "string",
    "password_hash": "string",
    "status": "number",
    "last_login": "datetime",
    "login_attempts": "number",
    "locked_until": "datetime",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  "auth_token": {
    "access_token": "string",
    "token_type": "bearer",
    "expires_in": "number",
    "refresh_token": "string",
    "device_id": "string"
  },
  "sms_verification": {
    "mobile": "string",
    "code": "string",
    "type": "login|register|reset",
    "expires_at": "datetime",
    "attempts": "number",
    "verified": "boolean"
  }
}
```

**接口需求**：
- **一键登录接口**：`POST /api/auth/quick-login`
  ```json
  {
    "mobile": "string",
    "sms_code": "string"
  }
  ```
- **本机号码一键登录**：`POST /api/auth/native-login`
  ```json
  {
    "sms_code": "string"
  }
  ```
- **密码登录接口**：`POST /api/auth/login`
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **获取验证码**：`POST /api/auth/sms-code`
  ```json
  {
    "mobile": "string",
    "type": "login|register|reset"
  }
  ```
- **生物识别登录**：`POST /api/auth/biometric-login`
  ```json
  {
    "device_id": "string",
    "biometric_token": "string"
  }
  ```
- **Token验证**：`POST /api/auth/verify-token`
- **Token刷新**：`POST /api/auth/refresh`
- **退出登录**：`POST /api/auth/logout`
- **账户状态检查**：`GET /api/auth/status`

#### 🎨 交互设计
- **页面布局**：
  - 主登录界面采用简洁设计，突出一键登录功能
  - 顶部显示应用Logo和欢迎文案
  - 中央区域为主要登录表单（手机号输入框 + 获取验证码按钮）
  - 顶部提供"本机号码一键登录"快捷选项
  - 底部提供"密码登录"和"生物识别登录"切换选项
- **交互细节**：
  - **一键登录**：
    - 手机号输入框：支持格式验证，错误时红色边框提示
    - 获取验证码按钮：60秒倒计时防重复，倒计时期间显示剩余秒数
    - 验证码输入框：6位数字，自动验证并登录/注册
    - 登录按钮：验证码输入完成后自动激活
  - **本机号码一键登录**：
    - 一键获取本机号码并自动发送验证码
    - 显示获取到的手机号，用户确认后输入验证码
    - 用户只需输入验证码即可完成登录/注册
  - **密码登录**：
    - 用户名/手机号输入框：支持两种格式输入
    - 密码输入框：支持显示/隐藏切换，密码强度提示
    - 忘记密码链接：跳转到密码重置流程
    - 记住密码选项：本地安全存储登录信息
  - **生物识别登录**：
    - 指纹/面容ID按钮：显示对应的生物识别图标
    - 一键验证：调用系统生物识别API，快速完成登录
    - 回退机制：生物识别失败时自动回退到其他登录方式
  - **状态保持**：Token自动续期，7天免登录，支持生物识别快速登录
- **状态变化**：
  - 未登录：显示登录表单和可用的登录方式
  - 登录中：显示加载动画，禁用表单
  - 已登录：跳转主界面
  - 登录失败：显示错误提示，重置表单状态
- **错误处理**：
  - 网络错误：显示网络连接失败提示，提供重试按钮
  - 验证码错误：显示验证码错误提示，支持重新获取
  - 密码错误：显示密码错误提示，记录错误次数
  - 生物识别失败：提示失败原因，引导使用其他登录方式

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟用户按照标准流程操作，功能正常工作
  - **测试场景**：用户输入有效手机号和验证码，完成一键登录流程
  - **预期结果**：登录成功，生成有效Token，跳转主界面
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：无效手机号、错误验证码、网络异常、密码错误等情况
  - **预期结果**：显示合适的错误提示，系统状态保持稳定
- [ ] **性能要求验收**：功能满足性能指标要求
  - **测试场景**：正常负载和压力测试，1000+并发用户登录
  - **预期结果**：登录响应时间 < 2秒，系统稳定运行

**具体验收项目**：
- [ ] **一键登录验收**：用户能够通过手机号+验证码快速登录，无账号时自动注册
- [ ] **本机号码一键登录验收**：系统能够自动识别本机号码并完成登录/注册流程
- [ ] **密码登录验收**：传统用户名/密码登录功能正常，支持手机号和用户名登录
- [ ] **生物识别登录验收**：指纹/面容ID登录功能正常，支持快速身份验证
- [ ] **自动注册验收**：一键登录时，新用户能够自动注册并直接登录成功
- [ ] **Token管理验收**：JWT Token生成、验证、刷新机制正常，安全性达标
- [ ] **设备绑定验收**：登录成功后自动注册设备信息，支持多设备管理
- [ ] **安全性验收**：密码加密存储，Token安全传输，防止暴力破解
- [ ] **用户体验验收**：登录流程简化，用户操作步骤 ≤ 3步完成登录

---

### 5.2 用户身份管理模块 - 个人资料系统

**优先级**: Must
**复杂度**: 中等
**预估工期**: 6人天
**依赖模块**: 用户身份认证模块、设备管理系统
**对应代码模块**: `cheestack-flt/lib/features/profile/`

#### 🎯 功能概述
- **功能定义**：提供完整的用户个人资料管理、账户设置和权限管理功能，支持用户信息维护和个性化配置
- **核心价值**：提供完善的用户资料管理体验，支持个性化设置，确保用户数据安全和隐私保护
- **使用场景**：用户管理个人资料、修改密码、设置学习偏好、管理账户安全、配置应用设置
- **业务规则**：
  - 用户资料管理：头像上传、个人信息编辑、简介设置
  - 密码安全管理：密码修改、密码强度检测、安全问题设置
  - 学习偏好配置：学习模式、复习数量、音频播放等个性化设置
  - 账户安全设置：生物识别开关、设备管理、登录历史查看
  - 隐私设置：数据导出、账户注销、隐私权限管理
  - 应用设置：主题切换、语言设置、通知配置
- **前置条件**：用户已完成身份认证，具有有效的登录状态

#### 🔄 功能流程设计
```mermaid
flowchart TD
    A[用户进入个人中心] --> B[显示用户资料]
    B --> C{选择功能模块}
    C -->|编辑资料| D[个人信息编辑]
    C -->|密码管理| E[密码安全设置]
    C -->|学习设置| F[学习偏好配置]
    C -->|账户安全| G[安全设置管理]
    C -->|隐私设置| H[隐私权限管理]
    D --> I[头像上传/信息修改]
    E --> J[密码修改/安全验证]
    F --> K[学习参数配置]
    G --> L[生物识别/设备管理]
    H --> M[数据导出/账户注销]
    I --> N[保存并验证]
    J --> N
    K --> N
    L --> N
    M --> O[确认操作]
    N --> P{验证是否通过?}
    O --> Q{是否确认?}
    P -->|是| R[更新用户数据]
    P -->|否| S[显示错误提示]
    Q -->|是| T[执行敏感操作]
    Q -->|否| B
    R --> U[同步到云端]
    T --> U
    S --> B
    U --> V[操作完成]
```

**流程步骤详解**：
1. **触发阶段**：用户进入个人中心，系统加载用户当前资料和设置
2. **验证阶段**：
   - 权限验证：检查用户对各项设置的修改权限
   - 数据验证：验证输入数据的格式和合法性
   - 安全验证：敏感操作需要二次身份验证
3. **执行阶段**：
   - 资料编辑：头像上传、个人信息修改、简介设置
   - 密码管理：密码强度检测、安全密码修改
   - 设置配置：学习偏好、应用设置、隐私设置
   - 安全管理：生物识别开关、设备管理、权限设置
4. **反馈阶段**：
   - 实时反馈：设置修改后立即生效并显示确认
   - 操作确认：重要操作前显示确认对话框
5. **异常处理**：
   - 数据冲突：多设备同时修改时的冲突检测和解决
   - 验证失败：密码错误、权限不足等情况的处理
   - 网络异常：离线修改和同步机制

**边界条件**：
- **正常边界**：头像文件最大5MB，用户名2-20字符，简介最多200字符
- **异常边界**：密码修改24小时内最多3次，敏感操作需要验证身份
- **性能边界**：设置保存响应时间<1秒，头像上传时间<10秒

#### 📊 数据需求
**数据结构**：
```json
{
  "user_profile": {
    "user_id": "string",
    "username": "string",
    "email": "string",
    "avatar": "string",
    "intro": "string",
    "gender": "string",
    "birthday": "date",
    "location": "string",
    "updated_at": "datetime"
  },
  "user_config": {
    "user_id": "string",
    "is_auto_play_audio": "boolean",
    "is_auto_play_ai_audio": "boolean",
    "review_number": "number",
    "study_number": "number",
    "editing_book_id": "string",
    "current_study_id": "string",
    "study_type": "number",
    "theme": "string",
    "language": "string",
    "notification_enabled": "boolean",
    "biometric_enabled": "boolean"
  },
  "user_security": {
    "user_id": "string",
    "password_updated_at": "datetime",
    "password_change_count": "number",
    "two_factor_enabled": "boolean",
    "security_questions": "array",
    "login_history": "array",
    "trusted_devices": "array"
  },
  "user_privacy": {
    "user_id": "string",
    "data_sharing_enabled": "boolean",
    "analytics_enabled": "boolean",
    "marketing_enabled": "boolean",
    "profile_visibility": "public|private|friends",
    "data_export_requests": "array"
  }
}
```

**接口需求**：
- **获取用户资料**：`GET /api/profile/me`
- **更新用户资料**：`PUT /api/profile/me`
- **头像上传**：`POST /api/profile/avatar`
- **修改密码**：`PUT /api/profile/password`
- **获取用户配置**：`GET /api/profile/config`
- **更新用户配置**：`PUT /api/profile/config`
- **安全设置**：`GET/PUT /api/profile/security`
- **隐私设置**：`GET/PUT /api/profile/privacy`
- **数据导出**：`POST /api/profile/export`
- **账户注销**：`DELETE /api/profile/account`

#### 🎨 交互设计
- **个人资料页面**：
  - 头像区域：支持点击更换头像，显示上传进度
  - 基本信息：用户名、邮箱、简介等可编辑字段
  - 统计信息：学习天数、卡片数量、成就等只读信息
- **设置页面**：
  - 分类设置：学习设置、账户安全、隐私设置、应用设置
  - 开关控件：生物识别、通知、自动播放等开关设置
  - 选择器：主题、语言、学习模式等选择设置
- **密码管理页面**：
  - 当前密码验证：修改前需要输入当前密码
  - 新密码设置：实时密码强度检测和提示
  - 确认密码：确保两次输入一致
- **安全中心页面**：
  - 登录历史：显示最近的登录记录和设备信息
  - 设备管理：查看和管理已登录的设备
  - 安全设置：两步验证、安全问题等设置

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟用户完整的资料管理流程，功能正常工作
  - **测试场景**：用户修改个人资料、更换头像、调整设置
  - **预期结果**：所有修改正确保存，设置立即生效，数据同步正常
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：大文件上传、网络中断、权限不足等情况
  - **预期结果**：显示合适的错误提示，数据不丢失，操作可恢复
- [ ] **性能要求验收**：功能满足性能指标要求
  - **测试场景**：大量设置修改、头像上传、数据导出等操作
  - **预期结果**：设置保存时间 < 1秒，头像上传时间 < 10秒

**具体验收项目**：
- [ ] **资料管理验收**：用户信息编辑、头像上传、简介设置功能正常
- [ ] **密码管理验收**：密码修改、强度检测、安全验证功能完善
- [ ] **设置配置验收**：学习偏好、应用设置、个性化配置功能正常
- [ ] **安全管理验收**：生物识别、设备管理、登录历史功能完整
- [ ] **隐私保护验收**：数据导出、账户注销、隐私设置功能正常
- [ ] **数据同步验收**：设置修改能及时同步到其他设备

---


### 5.4 内容创作管理系统 - 书籍卡片创作

**优先级**: Must
**复杂度**: 复杂
**预估工期**: 15人天
**依赖模块**: 本地数据库存储、用户身份认证模块、多媒体处理系统
**对应代码模块**: `cheestack-flt/lib/features/creation/`

#### 🎯 功能概述
- **功能定义**：提供完整的学习内容创建、编辑和管理系统，支持书籍、章节、卡片的结构化创作，集成多媒体内容和智能化辅助功能
- **核心价值**：为用户提供零门槛的内容创作体验，通过智能化工具和模板系统，让任何用户都能高效创建高质量的学习材料
- **使用场景**：用户创建个人学习书籍、制作学习卡片、组织知识体系、分享学习内容，适用于各类学科和知识领域
- **业务规则**：
  - 支持多层级书籍结构：书籍 → 章节 → 卡片（最多3级章节）
  - 多媒体卡片支持：文字、图片、音频、视频等多种媒体类型
  - 智能创作辅助：模板推荐、标签建议、内容优化、重复检测
  - 协作和分享：支持书籍共享、协作编辑、权限管理
  - 本地优先存储：所有创作内容优先保存本地，支持离线创作
  - 版本管理：保留编辑历史，支持版本对比和回退
- **前置条件**：用户已完成身份认证，具有创作权限，本地数据库已初始化

#### 🔄 功能流程设计
```mermaid
flowchart TD
    A[用户进入创作模块] --> B[选择创作类型]
    B --> C{创建新书籍?}
    C -->|是| D[选择书籍模板]
    C -->|否| E[选择现有书籍]
    D --> F[填写书籍基本信息]
    F --> G[设计章节结构]
    G --> H[创建学习卡片]
    E --> I{操作类型?}
    I -->|编辑书籍| J[修改书籍信息]
    I -->|管理章节| K[章节增删改]
    I -->|创建卡片| H
    H --> L[选择卡片类型]
    L --> M[编辑卡片内容]
    M --> N{添加媒体?}
    N -->|是| O[上传媒体文件]
    N -->|否| P[设置标签分类]
    O --> Q[媒体处理和预览]
    Q --> P
    P --> R[保存卡片]
    R --> S[本地存储]
    S --> T[后台同步到云端]
    J --> U[更新书籍]
    K --> V[重组章节结构]
    U --> S
    V --> S
    T --> W[完成创作]
```

**流程步骤详解**：
1. **触发阶段**：用户从主页进入创作模块，选择创建新内容或编辑现有内容
2. **验证阶段**：
   - 权限验证：检查用户的创作权限和存储空间
   - 数据验证：验证书籍名称唯一性、内容格式合法性
   - 资源验证：检查媒体文件格式、大小限制
3. **执行阶段**：
   - 书籍创建：基于模板或自定义创建书籍结构
   - 内容编辑：富文本编辑、媒体插入、格式化处理
   - 智能辅助：自动标签推荐、内容建议、重复检测
4. **反馈阶段**：
   - 实时预览：编辑过程中实时显示最终效果
   - 自动保存：定时保存防止数据丢失
5. **异常处理**：
   - 网络异常：本地保存，网络恢复后同步
   - 存储异常：提示用户并提供数据恢复选项
   - 格式错误：自动转换或提示用户修正

**边界条件**：
- **正常边界**：书籍名称1-100字符，章节层级最多3级，单个卡片内容最大50KB
- **异常边界**：图片最大10MB，音频最大50MB，视频最大100MB
- **性能边界**：卡片创建响应时间<2秒，媒体上传时间<30秒

#### 📊 数据需求
**数据结构**：
```json
{
  "book": {
    "id": "string",
    "user_id": "string",
    "name": "string",
    "brief": "string",
    "cover": "string",
    "privacy": "private|public|vip",
    "chapters": [
      {
        "id": "string",
        "name": "string",
        "order": "number",
        "parent_id": "string|null",
        "cards": ["card_id_1", "card_id_2"]
      }
    ],
    "tags": ["tag1", "tag2"],
    "created_at": "datetime",
    "updated_at": "datetime",
    "synced_at": "datetime",
    "is_dirty": "boolean"
  },
  "card": {
    "id": "string",
    "user_id": "string",
    "book_id": "string",
    "chapter_id": "string",
    "type": "basic|cloze|choice|image|audio|video",
    "title": "string",
    "question": "string",
    "answer": "string",
    "extra": "object",
    "assets": [
      {
        "id": "string",
        "type": "image|audio|video",
        "url": "string",
        "local_path": "string",
        "metadata": "object"
      }
    ],
    "tags": ["tag1", "tag2"],
    "created_at": "datetime",
    "updated_at": "datetime",
    "is_dirty": "boolean"
  }
}
```

**接口需求**：
- **书籍管理**：`GET/POST/PUT/DELETE /api/v1/books`
- **章节管理**：`GET/POST/PUT/DELETE /api/v1/books/{book_id}/chapters`
- **卡片管理**：`GET/POST/PUT/DELETE /api/v1/cards`
- **媒体上传**：`POST /api/v1/assets/upload`
- **模板获取**：`GET /api/v1/templates`
- **标签管理**：`GET/POST/PUT/DELETE /api/v1/tags`
- **批量操作**：`POST /api/v1/books/batch`, `POST /api/v1/cards/batch`
- **导入导出**：`POST /api/v1/import`, `GET /api/v1/export`

#### 🎨 交互设计
- **创作主页**：
  - 创作入口：醒目的"创建新书籍"按钮，支持快速创建和模板选择
  - 书籍列表：网格/列表视图切换，支持搜索、筛选、排序
  - 统计面板：显示创作数量、最近活动、热门内容等统计信息
- **书籍编辑器**：
  - 信息编辑：书名、简介、封面、隐私设置等基本信息编辑
  - 章节管理：可视化树状结构，支持拖拽排序和层级调整
  - 模板选择：提供学科分类模板，支持预览和一键应用
- **卡片编辑器**：
  - 所见即所得：实时预览卡片最终效果，双面卡片设计
  - 富文本工具栏：格式化、颜色、字体、列表、公式等编辑工具
  - 媒体插入：拖拽上传，支持图片、音频、视频，自动生成预览
  - 卡片类型：基础、填空、选择题、图片、音频、视频等类型选择
- **标签管理**：
  - 智能推荐：基于内容自动推荐标签，支持快速添加
  - 标签编辑：创建、编辑、删除标签，支持颜色和层级设置
  - 批量操作：为多张卡片批量添加或修改标签
- **协作分享**：
  - 权限设置：所有者、编辑者、查看者权限管理
  - 分享选项：链接分享、二维码分享、社区发布
  - 版本历史：编辑历史记录，支持版本对比和回退
- **移动端优化**：
  - 触屏友好：针对触屏操作优化的界面设计
  - 语音输入：支持语音转文字，提高移动端输入效率
  - 离线创作：支持离线编辑，网络恢复后自动同步

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟用户完整创作流程，所有功能正常工作
  - **测试场景**：创建书籍、设计章节、制作各类卡片、添加媒体内容
  - **预期结果**：创作流程顺畅，内容保存正确，同步功能正常
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：网络中断、大文件上传、格式错误、存储空间不足
  - **预期结果**：显示合适错误提示，提供解决方案，数据不丢失
- [ ] **性能要求验收**：功能满足性能指标要求
  - **测试场景**：大量内容创作、并发编辑、媒体处理
  - **预期结果**：响应时间 < 2秒，支持10万+卡片管理

**具体验收项目**：
- [ ] **书籍管理验收**：书籍创建、编辑、删除功能完整，支持模板和导入
- [ ] **章节管理验收**：多级章节结构管理，支持拖拽排序和批量操作
- [ ] **卡片创作验收**：多种卡片类型支持，富文本编辑功能完善
- [ ] **多媒体支持验收**：图片、音频、视频上传和处理功能正常
- [ ] **智能辅助验收**：标签推荐、内容建议、重复检测功能准确
- [ ] **协作分享验收**：权限管理、分享功能、版本控制正常工作
- [ ] **离线创作验收**：离线编辑、自动保存、数据同步功能完善
- [ ] **移动端验收**：移动端界面适配良好，触屏操作流畅
- [ ] **数据安全验收**：内容加密存储，权限控制严格，数据备份完整

---

---


### 5.5 设备管理系统 - 多设备身份管理

**优先级**: Must
**复杂度**: 中等
**预估工期**: 6人天
**依赖模块**: 用户身份认证模块

#### 🎯 功能概述
- **功能定义**：提供完整的用户设备管理功能，支持多设备登录和设备安全控制
- **核心价值**：确保用户账户安全，支持多设备无缝切换，提供设备使用监控和管理能力
- **使用场景**：用户在多个设备（手机、平板、电脑）上使用应用，需要管理和控制设备访问权限，监控设备使用情况
- **业务规则**：
  - 设备自动识别和注册：首次登录时自动注册设备信息
  - 设备信息管理：记录设备名称、类型、版本、IP地址等详细信息
  - 设备状态跟踪：实时监控设备活跃状态和最后使用时间
  - 设备安全验证：JWT Token与设备ID绑定，防止Token盗用
  - 设备远程管理：支持查看、重命名、停用设备功能
  - 设备数量限制：免费用户最多3台设备，付费用户无限制
- **前置条件**：用户已完成身份认证，设备具有唯一标识符

#### 🔄 功能流程设计
```mermaid
flowchart TD
    A[用户登录成功] --> B{设备已注册?}
    B -->|否| C[获取设备信息]
    B -->|是| D[更新设备信息]
    C --> E[检查设备数量限制]
    E --> F{是否超出限制?}
    F -->|是| G[提示升级或移除设备]
    F -->|否| H[注册新设备]
    G --> I[用户选择操作]
    I --> J{移除旧设备?}
    J -->|是| K[停用选中设备]
    J -->|否| L[升级账户]
    K --> H
    L --> H
    H --> M[生成设备绑定Token]
    D --> M
    M --> N[用户正常使用]
    N --> O[定期更新活跃状态]
    O --> P{用户管理设备?}
    P -->|是| Q[进入设备管理页面]
    P -->|否| N
    Q --> R[查看/重命名/停用设备]
    R --> S[更新设备状态]
    S --> N
```

**流程步骤详解**：
1. **触发阶段**：用户登录成功后，系统自动检查当前设备注册状态
2. **验证阶段**：
   - 设备识别：通过设备唯一标识符检查是否已注册
   - 权限验证：检查用户设备数量是否超出限制
   - 安全验证：验证设备信息的合法性和完整性
3. **执行阶段**：
   - 新设备注册：收集设备信息，生成设备记录，绑定用户账户
   - 设备信息更新：更新已注册设备的系统版本、应用版本等信息
   - Token绑定：将JWT Token与设备ID绑定，确保安全性
4. **反馈阶段**：
   - 成功：设备注册/更新成功，用户可正常使用
   - 失败：显示错误信息，提供解决方案
5. **异常处理**：
   - 设备数量超限：提示用户升级账户或移除旧设备
   - 设备信息异常：记录异常日志，使用默认值继续流程
   - 网络异常：本地缓存设备信息，网络恢复后同步

**边界条件**：
- **正常边界**：设备名称1-50字符，支持的设备类型（iOS/Android/Web）
- **异常边界**：免费用户最多3台设备，设备离线30天自动标记为非活跃
- **性能边界**：设备注册响应时间<1秒，支持10万+设备管理

#### 📊 数据需求
**数据结构**：
```json
{
  "device": {
    "id": "string",
    "user_id": "string",
    "device_name": "string",
    "device_type": "ios|android|web",
    "device_model": "string",
    "os_version": "string",
    "app_version": "string",
    "device_token": "string",
    "device_fingerprint": "string",
    "user_agent": "string",
    "ip_address": "string",
    "location": "string",
    "last_active": "datetime",
    "is_active": "boolean",
    "login_count": "number",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  "device_limit": {
    "user_type": "free|premium",
    "max_devices": "number",
    "current_devices": "number"
  }
}
```

**接口需求**：
- **设备列表**：`GET /api/auth/devices/me`
  ```json
  {
    "devices": [
      {
        "id": "string",
        "device_name": "string",
        "device_type": "string",
        "last_active": "datetime",
        "is_current": "boolean"
      }
    ],
    "device_limit": {
      "max_devices": "number",
      "current_devices": "number"
    }
  }
  ```
- **注册设备**：`POST /api/auth/devices/register`
  ```json
  {
    "device_name": "string",
    "device_type": "string",
    "device_model": "string",
    "os_version": "string",
    "app_version": "string"
  }
  ```
- **更新设备**：`PUT /api/auth/devices/{device_id}`
- **停用设备**：`DELETE /api/auth/devices/{device_id}`
- **设备详情**：`GET /api/auth/devices/{device_id}`

#### 🎨 交互设计
- **设备管理页面**：
  - 设备列表：显示设备名称、类型、最后活跃时间
  - 当前设备标识：高亮显示当前使用的设备
  - 设备操作：重命名、查看详情、停用设备
- **设备限制提示**：
  - 超出限制时显示友好的提示界面
  - 提供升级账户和移除设备两种选择
  - 设备选择界面：显示设备使用频率，建议移除非活跃设备

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟用户在多设备间切换使用，功能正常工作
  - **测试场景**：用户在新设备登录，系统自动注册设备并绑定Token
  - **预期结果**：设备注册成功，Token正常工作，设备信息准确记录
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：设备数量超限、设备信息异常、网络中断等情况
  - **预期结果**：显示合适的错误提示，提供解决方案，系统状态稳定
- [ ] **性能要求验收**：功能满足性能指标要求
  - **测试场景**：大量设备注册和管理操作，并发访问测试
  - **预期结果**：设备注册响应时间 < 1秒，支持10万+设备管理

**具体验收项目**：
- [ ] **设备注册验收**：用户登录时自动识别和注册设备，设备信息完整准确
- [ ] **设备管理验收**：用户可以查看、重命名、停用设备，操作响应及时
- [ ] **安全验证验收**：JWT Token与设备绑定，防止Token盗用，设备验证正常
- [ ] **状态跟踪验收**：设备活跃状态和使用统计准确，实时更新
- [ ] **数量限制验收**：免费用户设备数量限制生效，超限时正确处理
- [ ] **多设备同步验收**：设备状态变更能及时同步到其他设备

---

### 5.6 智能间隔重复系统 - FSRS算法核心

**优先级**: Must
**复杂度**: 复杂
**预估工期**: 12人天
**依赖模块**: 本地数据库存储、学习卡片系统

#### 🎯 功能概述
- **功能定义**：基于FSRS算法的智能复习调度系统，根据用户记忆表现动态调整复习间隔，实现科学高效的学习
- **核心价值**：通过科学的记忆算法，显著提升学习效率和记忆保持率，为用户提供个性化的学习体验
- **使用场景**：用户学习新卡片和复习旧卡片时，系统智能安排最佳复习时间，根据用户表现调整学习策略
- **业务规则**：
  - 支持1-5分的记忆表现评分（1=完全忘记，2=困难，3=一般，4=容易，5=非常容易）
  - 动态调整记忆稳定性（Stability）和难度系数（Difficulty）
  - 个性化参数优化：根据用户历史表现调整算法参数
  - 学习记录跟踪：完整记录每次学习的时间、评分、间隔等数据
  - 复习调度优化：优先安排即将遗忘的卡片，平衡学习负担
  - 算法参数可配置：支持不同学习场景的参数调整
- **前置条件**：用户已创建学习卡片，系统已初始化FSRS算法参数

#### 🔄 功能流程设计
```mermaid
flowchart TD
    A[用户开始学习] --> B{新卡片还是复习?}
    B -->|新卡片| C[初始化FSRS参数]
    B -->|复习| D[获取卡片当前状态]
    C --> E[展示学习内容]
    D --> F[检查是否到期]
    F -->|未到期| G[跳过此卡片]
    F -->|已到期| E
    E --> H[用户学习并评分]
    H --> I[FSRS算法计算]
    I --> J[更新记忆参数]
    J --> K[计算下次复习时间]
    K --> L[保存学习记录]
    L --> M{继续学习?}
    M -->|是| N[获取下一张卡片]
    M -->|否| O[生成学习报告]
    N --> B
    G --> N
    O --> P[结束学习]
```

**流程步骤详解**：
1. **触发阶段**：用户选择学习模式（新学习/复习），系统准备相应的卡片队列
2. **验证阶段**：
   - 卡片状态检查：验证卡片是否需要复习（基于到期时间）
   - 参数有效性：检查FSRS算法参数的完整性和合理性
   - 学习权限：验证用户对该书籍的学习权限
3. **执行阶段**：
   - 新卡片学习：初始化Stability=0.4, Difficulty=5, 设置初始间隔
   - 复习卡片：基于当前参数和用户评分，计算新的记忆参数
   - FSRS核心计算：
     - 根据评分更新Difficulty: D' = D + w[6] * (R - 3)
     - 计算Stability: S' = S * (1 + exp(w[8]) * (11 - D) * S^(-w[9]) * (exp((1 - R) * w[10]) - 1))
     - 计算下次间隔: I = S' * (R^w[11] - 1)
4. **反馈阶段**：
   - 实时反馈：显示学习进度、记忆强度变化
   - 学习统计：更新今日学习数量、正确率等指标
5. **异常处理**：
   - 算法异常：使用默认参数继续计算，记录异常日志
   - 数据异常：验证输入参数范围，异常时使用安全默认值
   - 性能异常：算法计算超时时使用简化计算

**边界条件**：
- **正常边界**：评分1-5分，Stability范围0.1-36500天，Difficulty范围1-10
- **异常边界**：算法计算时间>100ms时使用缓存结果，参数异常时重置为默认值
- **性能边界**：单次计算<50ms，批量计算1000张卡片<5秒

#### 📊 数据需求
**数据结构**：
```json
{
  "fsrs_card": {
    "card_id": "string",
    "user_id": "string",
    "stability": "number",
    "difficulty": "number",
    "elapsed_days": "number",
    "scheduled_days": "number",
    "reps": "number",
    "lapses": "number",
    "state": "new|learning|review|relearning",
    "last_review": "datetime",
    "due": "datetime"
  },
  "study_record": {
    "id": "string",
    "user_id": "string",
    "card_id": "string",
    "rating": "number",
    "state": "string",
    "review_time": "datetime",
    "elapsed_days": "number",
    "scheduled_days": "number",
    "stability_before": "number",
    "stability_after": "number",
    "difficulty_before": "number",
    "difficulty_after": "number"
  },
  "fsrs_params": {
    "user_id": "string",
    "w": "array[17]",
    "request_retention": "number",
    "maximum_interval": "number",
    "updated_at": "datetime"
  }
}
```

**接口需求**：
- **获取复习卡片**：`GET /api/study/cards/due`
- **提交学习记录**：`POST /api/study/records`
  ```json
  {
    "card_id": "string",
    "rating": "number",
    "review_time": "datetime"
  }
  ```
- **FSRS参数配置**：`GET/PUT /api/study/fsrs-params`
- **学习统计**：`GET /api/study/stats`
- **算法优化**：`POST /api/study/optimize-params`

#### 🎨 交互设计
- **学习界面**：
  - 卡片展示区：清晰显示问题和答案，支持多媒体内容
  - 评分按钮：1-5分评分，每个分数有明确的文字说明
  - 进度指示：显示当前学习进度和剩余卡片数量
  - 记忆强度指示：可视化显示当前卡片的记忆稳定性
- **评分界面**：
  - 评分选项：
    - 1分（完全忘记）：红色，建议重新学习
    - 2分（困难）：橙色，需要加强练习
    - 3分（一般）：黄色，正常复习节奏
    - 4分（容易）：绿色，可以延长间隔
    - 5分（非常容易）：蓝色，大幅延长间隔
  - 下次复习时间预览：显示不同评分对应的下次复习时间
- **学习反馈**：
  - 实时统计：显示今日学习数量、正确率、学习时长
  - 记忆曲线：可视化展示记忆强度变化趋势
  - 个性化建议：基于学习表现提供学习建议

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟用户完整学习流程，FSRS算法计算准确
  - **测试场景**：用户学习新卡片并进行多轮复习，评分1-5分
  - **预期结果**：算法正确计算记忆参数，复习间隔合理递增
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：网络中断、数据异常、参数错误等情况
  - **预期结果**：算法稳定运行，使用默认参数继续服务
- [ ] **性能要求验收**：算法满足性能指标要求
  - **测试场景**：大量卡片计算、并发学习请求
  - **预期结果**：单次计算时间 < 50ms，支持10万+卡片

**具体验收项目**：
- [ ] **FSRS算法验收**：算法实现正确，参数计算准确，符合FSRS规范
- [ ] **个性化优化验收**：根据用户历史数据优化算法参数，提升学习效果
- [ ] **复习调度验收**：智能安排复习顺序，优先处理即将遗忘的卡片
- [ ] **学习记录验收**：完整记录学习过程，支持学习分析和回顾
- [ ] **参数配置验收**：支持算法参数自定义配置，满足不同学习需求
- [ ] **数据一致性验收**：学习数据准确保存，多设备间数据同步正确
- [ ] **算法稳定性验收**：长期使用算法参数稳定，不出现异常波动

---

### 5.7 多媒体学习卡片 - 丰富学习内容

**优先级**: Must
**复杂度**: 中等
**预估工期**: 10人天
**依赖模块**: 知识管理系统、本地数据库存储

#### 🎯 功能概述
- **功能定义**：支持文字、图片、音频、视频的多媒体学习卡片系统，提供丰富的内容创建和编辑功能
- **核心价值**：通过多媒体内容提升学习体验，支持不同学习风格，提高知识记忆效果
- **使用场景**：用户创建和编辑学习卡片，支持各种媒体类型的学习内容，适用于语言学习、专业知识、技能培训等场景
- **业务规则**：
  - 支持富文本编辑：文字格式化、颜色、字体、大小等
  - 媒体文件上传：图片（JPG/PNG/GIF，最大10MB）、音频（MP3/WAV，最大50MB）、视频（MP4，最大100MB）
  - 卡片模板系统：提供常用模板（单词卡、问答卡、填空卡等）
  - 批量导入导出：支持CSV、Excel、Anki格式的批量操作
  - 卡片标签分类：支持多级标签，便于组织和搜索
  - 卡片版本管理：保留编辑历史，支持版本回退
- **前置条件**：用户已创建学习书籍，具有编辑权限

#### 🔄 功能流程设计
```mermaid
flowchart TD
    A[用户创建/编辑卡片] --> B[选择卡片模板]
    B --> C[编辑卡片内容]
    C --> D{添加媒体文件?}
    D -->|是| E[选择媒体类型]
    D -->|否| F[设置卡片属性]
    E --> G[上传媒体文件]
    G --> H[文件格式验证]
    H --> I{格式是否支持?}
    I -->|否| J[显示错误提示]
    I -->|是| K[文件大小检查]
    K --> L{大小是否超限?}
    L -->|是| M[压缩或提示用户]
    L -->|否| N[上传到本地存储]
    N --> O[生成媒体预览]
    O --> F
    J --> E
    M --> N
    F --> P[添加标签分类]
    P --> Q[保存卡片]
    Q --> R[更新索引]
    R --> S[同步到云端]
    S --> T[完成创建/编辑]
```

**流程步骤详解**：
1. **触发阶段**：用户选择创建新卡片或编辑现有卡片
2. **验证阶段**：
   - 权限验证：检查用户对书籍的编辑权限
   - 格式验证：验证媒体文件格式和大小
   - 内容验证：检查必填字段和内容合法性
3. **执行阶段**：
   - 内容编辑：富文本编辑器支持格式化文本
   - 媒体处理：文件上传、压缩、预览生成
   - 属性设置：标签、难度、重要性等属性配置
4. **反馈阶段**：
   - 实时预览：编辑过程中实时显示卡片效果
   - 保存确认：显示保存状态和结果
5. **异常处理**：
   - 文件格式不支持：提示用户转换格式
   - 文件过大：自动压缩或提示用户减小文件
   - 网络异常：本地保存，网络恢复后同步

**边界条件**：
- **正常边界**：卡片标题1-200字符，内容最大10000字符，标签最多10个
- **异常边界**：图片最大10MB，音频最大50MB，视频最大100MB
- **性能边界**：媒体上传时间<30秒，卡片保存响应时间<2秒

#### 📊 数据需求
**数据结构**：
```json
{
  "card": {
    "id": "string",
    "book_id": "string",
    "user_id": "string",
    "title": "string",
    "front_content": "string",
    "back_content": "string",
    "card_type": "basic|cloze|image|audio|video",
    "template_id": "string",
    "tags": "array",
    "difficulty": "number",
    "importance": "number",
    "created_at": "datetime",
    "updated_at": "datetime",
    "version": "number"
  },
  "card_asset": {
    "id": "string",
    "card_id": "string",
    "asset_type": "image|audio|video",
    "file_name": "string",
    "file_path": "string",
    "file_size": "number",
    "mime_type": "string",
    "duration": "number",
    "thumbnail": "string",
    "created_at": "datetime"
  },
  "card_template": {
    "id": "string",
    "name": "string",
    "description": "string",
    "front_template": "string",
    "back_template": "string",
    "css": "string",
    "is_builtin": "boolean"
  }
}
```

**接口需求**：
- **卡片列表**：`GET /api/cards?book_id={book_id}`
- **创建卡片**：`POST /api/cards`
- **更新卡片**：`PUT /api/cards/{card_id}`
- **删除卡片**：`DELETE /api/cards/{card_id}`
- **上传媒体**：`POST /api/cards/{card_id}/assets`
- **卡片模板**：`GET /api/cards/templates`
- **批量导入**：`POST /api/cards/import`
- **批量导出**：`GET /api/cards/export`
- **搜索卡片**：`GET /api/cards/search?q={keyword}`

#### 🎨 交互设计
- **卡片编辑器**：
  - 模板选择：提供多种预设模板，支持自定义模板
  - 富文本编辑：工具栏包含格式化、颜色、字体等选项
  - 媒体插入：拖拽上传或点击选择文件，支持预览
  - 实时预览：编辑时实时显示卡片最终效果
- **媒体管理**：
  - 文件上传：支持拖拽上传，显示上传进度
  - 媒体预览：图片缩略图、音频播放器、视频播放器
  - 文件管理：重命名、删除、替换媒体文件
- **卡片列表**：
  - 网格/列表视图切换
  - 搜索和过滤：按标签、类型、创建时间等筛选
  - 批量操作：选择多张卡片进行批量编辑或删除
- **导入导出**：
  - 格式选择：支持CSV、Excel、Anki等格式
  - 字段映射：导入时可自定义字段对应关系
  - 进度显示：批量操作时显示处理进度

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟用户创建和编辑各种类型的学习卡片
  - **测试场景**：创建文字卡片、图片卡片、音频卡片、视频卡片
  - **预期结果**：所有媒体类型正常支持，编辑功能完整，保存成功
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：大文件上传、格式不支持、网络中断等情况
  - **预期结果**：显示合适的错误提示，提供解决方案，数据不丢失
- [ ] **性能要求验收**：功能满足性能指标要求
  - **测试场景**：大量卡片创建、大文件上传、批量操作
  - **预期结果**：媒体加载时间 < 3秒，支持100MB文件上传

**具体验收项目**：
- [ ] **多媒体支持验收**：支持图片、音频、视频等媒体类型，格式兼容性好
- [ ] **富文本编辑验收**：文本格式化功能完整，所见即所得编辑体验
- [ ] **模板系统验收**：提供丰富的卡片模板，支持自定义模板创建
- [ ] **批量操作验收**：导入导出功能正常，支持大量数据处理
- [ ] **搜索过滤验收**：卡片搜索准确快速，过滤条件丰富实用
- [ ] **媒体管理验收**：文件上传稳定，媒体预览正常，存储管理高效
- [ ] **版本控制验收**：卡片编辑历史完整，支持版本对比和回退

---

### 5.8 语音学习功能 - 多语言语音识别

**优先级**: Should
**复杂度**: 复杂
**预估工期**: 15人天
**依赖模块**: 多媒体学习卡片、本地数据库存储

#### 🎯 功能概述
- **功能定义**：集成Sherpa-ONNX本地语音识别引擎，支持多语言发音评估和语音练习功能
- **核心价值**：为语言学习者提供专业的发音练习和评估，提升口语能力，支持离线使用
- **使用场景**：语言学习者进行口语练习、发音纠正、听力训练，特别适用于英语、中文、日语等语言学习
- **业务规则**：
  - 支持多语言识别：中文（普通话）、英语（美式/英式）、日语、韩语等
  - 本地离线语音处理：使用Sherpa-ONNX引擎，无需网络连接
  - 发音准确度评分：基于声学模型评估，提供0-100分的准确度评分
  - 语音练习模式：跟读练习、自由发音、发音对比等多种模式
  - 语音数据管理：录音保存、回放、分析等功能
  - 个性化语音模型：根据用户发音特点优化识别准确度
- **前置条件**：设备支持麦克风录音，已下载对应语言的语音模型

#### 🔄 功能流程设计
```mermaid
flowchart TD
    A[用户选择语音练习] --> B[检查语音权限]
    B --> C{权限是否允许?}
    C -->|否| D[请求麦克风权限]
    C -->|是| E[选择练习语言]
    D --> F{用户是否授权?}
    F -->|否| G[显示权限说明]
    F -->|是| E
    E --> H[加载语音模型]
    H --> I{模型是否存在?}
    I -->|否| J[下载语音模型]
    I -->|是| K[开始录音]
    J --> L{下载是否成功?}
    L -->|否| M[显示下载失败]
    L -->|是| K
    K --> N[实时音频处理]
    N --> O[语音识别]
    O --> P[发音评估]
    P --> Q[生成评分报告]
    Q --> R[显示结果反馈]
    R --> S{继续练习?}
    S -->|是| K
    S -->|否| T[保存练习记录]
    T --> U[结束练习]
```

**流程步骤详解**：
1. **触发阶段**：用户选择语音练习功能，系统检查必要权限和资源
2. **验证阶段**：
   - 权限验证：检查麦克风录音权限
   - 模型验证：检查对应语言的语音识别模型是否存在
   - 环境验证：检查音频设备状态和环境噪音水平
3. **执行阶段**：
   - 音频采集：实时录制用户语音，采样率16kHz
   - 语音识别：使用Sherpa-ONNX引擎进行实时语音转文字
   - 发音评估：对比标准发音，计算准确度、流畅度、完整度
   - 结果生成：生成详细的发音评估报告和改进建议
4. **反馈阶段**：
   - 实时反馈：录音过程中显示音量指示和识别状态
   - 结果展示：可视化显示评分结果和发音对比
5. **异常处理**：
   - 权限拒绝：引导用户手动开启权限
   - 模型缺失：自动下载或提示用户下载
   - 识别失败：提供重试选项和环境改善建议

**边界条件**：
- **正常边界**：录音时长5-60秒，支持的语言模型大小50-200MB
- **异常边界**：环境噪音>60dB时提示用户，识别准确率<70%时建议重试
- **性能边界**：语音识别延迟<2秒，模型加载时间<10秒

#### 📊 数据需求
**数据结构**：
```json
{
  "voice_record": {
    "id": "string",
    "user_id": "string",
    "card_id": "string",
    "language": "string",
    "audio_file": "string",
    "duration": "number",
    "text_recognized": "string",
    "text_expected": "string",
    "accuracy_score": "number",
    "fluency_score": "number",
    "completeness_score": "number",
    "overall_score": "number",
    "created_at": "datetime"
  },
  "voice_model": {
    "language": "string",
    "model_name": "string",
    "model_path": "string",
    "model_size": "number",
    "version": "string",
    "download_url": "string",
    "is_downloaded": "boolean"
  },
  "voice_config": {
    "user_id": "string",
    "default_language": "string",
    "sensitivity": "number",
    "auto_play": "boolean",
    "save_recordings": "boolean"
  }
}
```

**接口需求**：
- **语音识别**：`POST /api/voice/recognize`
- **发音评估**：`POST /api/voice/evaluate`
- **语音模型列表**：`GET /api/voice/models`
- **下载语音模型**：`POST /api/voice/models/download`
- **语音记录**：`GET /api/voice/records`
- **语音配置**：`GET/PUT /api/voice/config`

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟用户进行语音练习，功能正常工作
  - **测试场景**：用户录制标准发音，系统正确识别并评分
  - **预期结果**：语音识别准确率 > 85%，发音评估合理准确
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：噪音环境、网络异常、权限拒绝等情况
  - **预期结果**：显示合适的错误提示，提供解决方案
- [ ] **性能要求验收**：功能满足性能指标要求
  - **测试场景**：长时间录音、多语言切换、离线使用
  - **预期结果**：语音识别延迟 < 2秒，支持完全离线使用

**具体验收项目**：
- [ ] **多语言支持验收**：支持中英日韩等多种语言，识别准确率达标
- [ ] **离线功能验收**：完全离线工作，无需网络连接
- [ ] **发音评估验收**：评分算法准确，能识别发音问题并给出建议
- [ ] **实时处理验收**：录音和识别实时进行，用户体验流畅
- [ ] **模型管理验收**：语音模型下载、更新、管理功能正常
- [ ] **权限处理验收**：麦克风权限请求和处理流程完善

---

### 5.9 知识管理系统 - 书籍章节管理

**优先级**: Must
**复杂度**: 中等
**预估工期**: 8人天
**依赖模块**: 本地数据库存储、用户身份认证模块

#### 🎯 功能概述
- **功能定义**：提供完整的书籍、章节、标签管理系统，支持知识分类和组织，为学习内容提供结构化管理
- **核心价值**：帮助用户有序组织学习内容，提高学习效率，支持大规模知识管理
- **使用场景**：用户创建学习书籍，按章节组织学习内容，使用标签分类，适用于系统性学习和知识积累
- **业务规则**：
  - 支持多级书籍章节结构：书籍 → 章节 → 子章节（最多3级）
  - 标签分类和搜索功能：支持多标签、标签层级、智能推荐
  - 批量操作和导入导出：支持书籍模板、批量创建、数据迁移
  - 学习进度跟踪：章节完成度、学习时长、掌握程度统计
  - 权限管理：支持书籍共享、协作编辑、访问控制
  - 书籍模板：提供常用学科的书籍结构模板
- **前置条件**：用户已完成身份认证，具有创建书籍的权限

#### 🔄 功能流程设计
```mermaid
flowchart TD
    A[用户创建/管理书籍] --> B[选择操作类型]
    B --> C{创建新书籍?}
    C -->|是| D[选择书籍模板]
    C -->|否| E[选择现有书籍]
    D --> F[填写书籍信息]
    F --> G[创建章节结构]
    G --> H[设置标签分类]
    H --> I[保存书籍]
    E --> J{管理操作类型?}
    J -->|编辑| K[修改书籍信息]
    J -->|章节管理| L[添加/编辑/删除章节]
    J -->|标签管理| M[管理标签体系]
    J -->|权限管理| N[设置共享权限]
    K --> O[更新书籍]
    L --> P[重新组织结构]
    M --> Q[更新标签索引]
    N --> R[通知相关用户]
    I --> S[同步到云端]
    O --> S
    P --> S
    Q --> S
    R --> S
    S --> T[完成操作]
```

**流程步骤详解**：
1. **触发阶段**：用户选择创建新书籍或管理现有书籍
2. **验证阶段**：
   - 权限验证：检查用户的书籍创建和编辑权限
   - 数据验证：验证书籍名称唯一性、章节结构合理性
   - 容量验证：检查用户书籍数量限制和存储空间
3. **执行阶段**：
   - 书籍创建：基于模板或自定义创建书籍结构
   - 章节管理：支持拖拽排序、层级调整、批量操作
   - 标签管理：创建标签体系、设置标签关系、自动标签推荐
   - 权限设置：配置书籍访问权限、协作者管理
4. **反馈阶段**：
   - 实时预览：编辑过程中实时显示书籍结构
   - 操作确认：重要操作前显示确认对话框
5. **异常处理**：
   - 数据冲突：多用户编辑时的冲突检测和解决
   - 存储异常：本地存储失败时的数据恢复
   - 网络异常：离线编辑和同步机制

**边界条件**：
- **正常边界**：书籍名称1-100字符，章节层级最多3级，标签最多50个
- **异常边界**：单个书籍最多1000个章节，标签名称最长20字符
- **性能边界**：书籍加载时间<2秒，搜索响应时间<500ms

#### 📊 数据需求
**数据结构**：
```json
{
  "book": {
    "id": "string",
    "user_id": "string",
    "name": "string",
    "description": "string",
    "cover_image": "string",
    "template_id": "string",
    "is_public": "boolean",
    "tags": "array",
    "card_count": "number",
    "progress": "number",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  "chapter": {
    "id": "string",
    "book_id": "string",
    "parent_id": "string",
    "name": "string",
    "description": "string",
    "order": "number",
    "level": "number",
    "card_count": "number",
    "completed_count": "number",
    "created_at": "datetime"
  },
  "tag": {
    "id": "string",
    "user_id": "string",
    "name": "string",
    "color": "string",
    "parent_id": "string",
    "usage_count": "number",
    "created_at": "datetime"
  },
  "book_permission": {
    "book_id": "string",
    "user_id": "string",
    "permission": "read|write|admin",
    "granted_by": "string",
    "granted_at": "datetime"
  }
}
```

**接口需求**：
- **书籍列表**：`GET /api/books`
- **创建书籍**：`POST /api/books`
- **更新书籍**：`PUT /api/books/{book_id}`
- **删除书籍**：`DELETE /api/books/{book_id}`
- **章节管理**：`GET/POST/PUT/DELETE /api/books/{book_id}/chapters`
- **标签管理**：`GET/POST/PUT/DELETE /api/tags`
- **书籍模板**：`GET /api/books/templates`
- **权限管理**：`GET/POST/PUT/DELETE /api/books/{book_id}/permissions`
- **搜索书籍**：`GET /api/books/search?q={keyword}`

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟用户创建和管理书籍章节，功能正常工作
  - **测试场景**：创建多级章节结构，添加标签分类，设置权限
  - **预期结果**：书籍结构清晰，标签分类准确，权限控制有效
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：大量数据处理、并发操作、数据冲突等情况
  - **预期结果**：系统稳定运行，数据完整性保证，冲突正确解决
- [ ] **性能要求验收**：功能满足性能指标要求
  - **测试场景**：大量书籍和章节管理，复杂搜索查询
  - **预期结果**：搜索响应时间 < 500ms，支持10万+卡片管理

**具体验收项目**：
- [ ] **书籍管理验收**：书籍创建、编辑、删除功能完整，支持模板使用
- [ ] **章节管理验收**：多级章节结构管理，支持拖拽排序和批量操作
- [ ] **标签系统验收**：标签创建、分类、搜索功能完善，支持智能推荐
- [ ] **权限控制验收**：书籍共享和协作功能正常，权限控制准确
- [ ] **搜索过滤验收**：书籍和章节搜索准确快速，过滤条件丰富
- [ ] **数据同步验收**：多设备间书籍数据同步正确，冲突处理合理

---

### 5.10 考试备考模式 - 模拟考试系统

**优先级**: Should
**复杂度**: 复杂
**预估工期**: 12人天
**依赖模块**: 多媒体学习卡片、FSRS算法系统、学习分析系统

#### 🎯 功能概述
- **功能定义**：提供完整的考试模拟系统，包括题库管理、模拟考试、成绩分析、错题集等功能
- **核心价值**：帮助用户进行考试备考，通过模拟考试检验学习效果，提供针对性的复习建议
- **使用场景**：学生备考各类考试、职场人士准备资格认证、用户自我检测学习成果
- **业务规则**：
  - 支持多种题型：单选题、多选题、填空题、简答题、判断题
  - 考试时间控制：设置考试时长，倒计时提醒，自动提交
  - 防作弊机制：题目乱序、选项乱序、切屏检测、时间限制
  - 自动评分：客观题自动评分，主观题关键词匹配评分
  - 成绩分析：详细的成绩报告、知识点掌握分析、薄弱环节识别
  - 错题集管理：自动收集错题，支持错题复习和重做
  - 考试模式：练习模式、模拟考试模式、冲刺模式
- **前置条件**：用户已创建相关学习内容，具有足够的题目数量

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟完整考试流程，功能正常工作
  - **测试场景**：用户参加模拟考试，完成各种题型，查看成绩分析
  - **预期结果**：考试流程完整，评分准确，成绩报告详细
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：考试中断、网络异常、时间到期等情况
  - **预期结果**：数据不丢失，能够恢复考试或正确处理异常
- [ ] **性能要求验收**：功能满足性能指标要求
  - **测试场景**：大量用户同时考试，复杂题目加载
  - **预期结果**：考试加载时间 < 3秒，支持1000+并发考试

**具体验收项目**：
- [ ] **题库管理验收**：支持多种题型创建和管理，题目导入导出正常
- [ ] **考试流程验收**：考试开始、答题、提交、评分流程完整
- [ ] **防作弊验收**：题目乱序、时间控制、切屏检测等机制有效
- [ ] **自动评分验收**：客观题评分准确，主观题评分合理
- [ ] **成绩分析验收**：成绩报告详细，知识点分析准确
- [ ] **错题集验收**：错题自动收集，错题复习功能完善

---

### 5.11 学习分析系统 - 数据可视化分析

**优先级**: Should
**复杂度**: 中等
**预估工期**: 10人天
**依赖模块**: FSRS算法系统、本地数据库存储

#### 🎯 功能概述
- **功能定义**：提供详细的学习数据分析和可视化，包括学习统计、记忆曲线、进度分析等功能
- **核心价值**：帮助用户了解学习效果，发现学习规律，获得个性化学习建议，提升学习效率
- **使用场景**：用户查看学习进度、分析学习效果、获取个性化建议、制定学习计划
- **业务规则**：
  - 多维度数据统计：学习时长、卡片数量、正确率、连续学习天数等
  - 交互式图表展示：记忆曲线、学习趋势、知识点掌握度、时间分布等
  - 个性化学习建议：基于学习数据提供针对性建议和优化方案
  - 学习报告生成：日报、周报、月报等定期学习报告
  - 对比分析：历史对比、目标对比、同类用户对比
  - 预测分析：学习效果预测、复习时间预测、目标达成预测
- **前置条件**：用户已有一定的学习数据积累

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟用户查看学习分析，功能正常工作
  - **测试场景**：用户查看各种学习统计图表，获取学习建议
  - **预期结果**：数据统计准确，图表展示清晰，建议合理实用
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：大量数据处理、图表渲染异常、数据缺失等情况
  - **预期结果**：系统稳定运行，异常情况有合理提示和处理
- [ ] **性能要求验收**：功能满足性能指标要求
  - **测试场景**：大量历史数据分析，复杂图表渲染
  - **预期结果**：图表加载时间 < 2秒，支持1年+历史数据分析

**具体验收项目**：
- [ ] **数据统计验收**：学习数据统计准确，覆盖各个维度
- [ ] **图表展示验收**：图表类型丰富，交互体验良好，视觉效果佳
- [ ] **个性化建议验收**：学习建议针对性强，实用性高
- [ ] **报告生成验收**：学习报告内容详细，格式美观，支持导出
- [ ] **对比分析验收**：各种对比分析功能正常，结果准确
- [ ] **预测分析验收**：学习效果预测合理，有一定准确性

---

### 5.12 本地数据库存储 - SQLite本地存储

**优先级**: Must
**复杂度**: 中等
**预估工期**: 8人天
**依赖模块**: 无（基础模块）

#### 🎯 功能概述
- **功能定义**：使用SQLite实现本地数据库存储，支持离线使用和数据持久化，为应用提供可靠的数据存储基础
- **核心价值**：确保应用完全离线可用，提供高性能的本地数据访问，保证数据安全和完整性
- **使用场景**：用户在无网络环境下正常使用应用，所有学习数据本地存储，网络恢复后自动同步
- **业务规则**：
  - 使用sqflite包实现SQLite数据库操作，支持Flutter平台
  - 完整的数据模型映射和DAO层设计，提供类型安全的数据访问
  - 支持事务操作和批量处理，确保数据一致性和操作效率
  - 数据完整性约束和索引优化，保证数据质量和查询性能
  - 自动数据库版本管理和迁移，支持应用升级时的数据结构变更
  - 数据备份和恢复机制，防止数据丢失
  - 支持数据加密存储，保护用户隐私
- **前置条件**：设备具有足够的存储空间，Flutter环境支持sqflite包

#### 📊 数据需求
**数据库表结构**：
- **users表**: 用户基本信息存储（id, mobile, username, email, avatar, etc.）
- **user_configs表**: 用户配置信息存储（学习偏好、界面设置等）
- **devices表**: 设备信息管理（设备标识、类型、状态等）
- **books表**: 学习书籍信息存储（书籍元数据、权限、统计等）
- **chapters表**: 书籍章节结构存储（层级关系、顺序等）
- **tags表**: 标签分类系统存储（标签层级、使用统计等）
- **cards表**: 学习卡片内容存储（问题、答案、类型等）
- **card_assets表**: 卡片多媒体资源存储（图片、音频、视频等）
- **fsrs_cards表**: FSRS算法参数存储（稳定性、难度等）
- **study_records表**: 学习记录和FSRS数据存储（评分、时间等）
- **voice_records表**: 语音学习记录存储（录音文件、评分等）
- **exam_records表**: 考试记录存储（成绩、答题详情等）
- **sync_records表**: 数据同步记录跟踪（同步状态、冲突等）

**DAO接口设计**：
```dart
// 用户数据访问层
abstract class UserDao {
  Future<User?> findByMobile(String mobile);
  Future<User?> findById(String userId);
  Future<int> insert(User user);
  Future<int> update(User user);
  Future<int> delete(String userId);
}

// 书籍数据访问层
abstract class BookDao {
  Future<List<Book>> findByUserId(String userId);
  Future<List<Book>> searchByName(String userId, String keyword);
  Future<Book?> findById(String bookId);
  Future<int> insert(Book book);
  Future<int> update(Book book);
  Future<int> delete(String bookId);
}

// 卡片数据访问层
abstract class CardDao {
  Future<List<Card>> findByBookId(String bookId);
  Future<List<Card>> findCardsForReview(String userId);
  Future<List<Card>> findNewCards(String userId, int limit);
  Future<Card?> findById(String cardId);
  Future<int> insert(Card card);
  Future<int> update(Card card);
  Future<int> delete(String cardId);
  Future<int> batchInsert(List<Card> cards);
}

// 学习记录数据访问层
abstract class StudyRecordDao {
  Future<List<StudyRecord>> getUserStudyStats(String userId);
  Future<int> getStudyStreak(String userId);
  Future<Map<String, dynamic>> getDailyStats(String userId, DateTime date);
  Future<int> insert(StudyRecord record);
  Future<List<StudyRecord>> findByDateRange(String userId, DateTime start, DateTime end);
}

// FSRS数据访问层
abstract class FSRSCardDao {
  Future<FSRSCard?> findByCardId(String cardId);
  Future<int> insert(FSRSCard fsrsCard);
  Future<int> update(FSRSCard fsrsCard);
  Future<List<FSRSCard>> findDueCards(String userId);
}
```

**数据库版本管理**：
```dart
class DatabaseMigration {
  static const int currentVersion = 1;

  static Future<void> onUpgrade(Database db, int oldVersion, int newVersion) async {
    for (int version = oldVersion + 1; version <= newVersion; version++) {
      await _migrateToVersion(db, version);
    }
  }

  static Future<void> _migrateToVersion(Database db, int version) async {
    switch (version) {
      case 1:
        await _createInitialTables(db);
        break;
      // 后续版本的迁移逻辑
    }
  }
}
```

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **正常流程验收**：模拟应用完整的数据存储和访问流程
  - **测试场景**：应用启动、数据创建、查询、更新、删除等操作
  - **预期结果**：数据库正确初始化，所有CRUD操作正常，数据持久化成功
- [ ] **异常流程验收**：系统能正确处理各种异常情况
  - **测试场景**：存储空间不足、数据库损坏、并发访问冲突等情况
  - **预期结果**：异常情况有合理处理，数据完整性得到保证
- [ ] **性能要求验收**：数据库操作满足性能指标要求
  - **测试场景**：大量数据操作、复杂查询、并发访问测试
  - **预期结果**：数据操作响应时间 < 500ms，支持10万+记录存储

**具体验收项目**：
- [ ] **数据库初始化验收**：应用首次启动能正确创建数据库和表结构
- [ ] **CRUD操作验收**：所有数据模型的增删改查操作正常，类型安全
- [ ] **事务处理验收**：复杂操作使用事务确保数据一致性，支持回滚
- [ ] **离线存储验收**：无网络环境下数据正常读写，完全离线可用
- [ ] **数据完整性验收**：外键约束和数据校验正确执行，防止脏数据
- [ ] **版本迁移验收**：数据库版本升级时数据迁移正确，无数据丢失
- [ ] **索引优化验收**：查询性能优化有效，复杂查询响应快速
- [ ] **数据备份验收**：数据备份和恢复机制正常，数据安全可靠
- [ ] **并发访问验收**：多线程并发访问数据库安全，无数据竞争
- [ ] **存储加密验收**：敏感数据加密存储，隐私保护到位

---

### 5.13 数据同步机制 - 多设备数据同步

**优先级**: Must
**复杂度**: 复杂
**预估工期**: 15人天
**依赖模块**: 本地数据库存储、设备管理系统、用户身份认证模块

#### 🎯 功能概述
- **功能定义**：提供完整的多设备数据同步机制，支持离线使用和智能冲突解决，确保用户数据在所有设备间保持一致
- **核心价值**：实现真正的多设备无缝切换体验，保证数据安全和一致性，支持离线使用场景
- **使用场景**：用户在手机、平板、电脑等多个设备间切换使用，学习数据实时保持同步一致，支持离线学习后的数据同步
- **业务规则**：
  - 实时增量数据同步：只同步变更的数据，提高同步效率
  - 智能冲突检测和自动解决：基于时间戳和版本号检测冲突，支持多种解决策略
  - 离线数据缓存和延迟同步：离线时本地缓存变更，网络恢复后自动同步
  - 数据完整性校验和修复：确保同步过程中数据不丢失、不损坏
  - 版本控制和回滚机制：支持数据版本管理和异常情况下的回滚
  - 同步状态监控和日志记录：实时监控同步状态，记录详细的同步日志
  - 优先级同步：重要数据优先同步，保证核心功能可用
- **前置条件**：用户已登录，设备已注册，网络连接可用（同步时）

#### 🔄 功能流程
```mermaid
flowchart TD
    A[本地数据变更] --> B[生成同步记录]
    B --> C[标记为待同步]
    C --> D{网络可用?}
    D -->|是| E[上传到服务器]
    D -->|否| F[等待网络恢复]
    F --> E
    E --> G{检测冲突?}
    G -->|无冲突| H[更新服务器数据]
    G -->|有冲突| I[冲突解决策略]
    I --> J[自动解决/手动解决]
    J --> H
    H --> K[推送到其他设备]
    K --> L[设备接收更新]
    L --> M[本地数据更新]
```

**详细流程说明**：
1. **数据变更检测**：监听本地数据变更，生成同步记录
2. **增量同步**：只同步变更的数据，减少网络传输
3. **冲突检测**：基于版本号和时间戳检测数据冲突
4. **冲突解决**：支持客户端优先、服务端优先、智能合并等策略
5. **数据推送**：将同步后的数据推送到用户的其他设备
6. **离线处理**：离线时缓存变更，联网后自动同步

#### 📊 数据需求
**数据结构**：
```json
{
  "sync_record": {
    "id": "string",
    "user_id": "string",
    "device_id": "string",
    "table_name": "string",
    "record_id": "string",
    "action": "create|update|delete",
    "status": "pending|syncing|success|failed|conflict",
    "client_version": "number",
    "server_version": "number",
    "data_before": "object",
    "data_after": "object",
    "sync_at": "datetime",
    "created_at": "datetime"
  },
  "conflict_log": {
    "id": "string",
    "sync_record_id": "string",
    "conflict_type": "string",
    "client_data": "object",
    "server_data": "object",
    "resolution": "client_wins|server_wins|merge|manual",
    "resolved_data": "object",
    "resolved_at": "datetime"
  },
  "sync_config": {
    "user_id": "string",
    "auto_sync_enabled": "boolean",
    "sync_interval": "number",
    "default_conflict_resolution": "string",
    "sync_books": "boolean",
    "sync_cards": "boolean",
    "sync_records": "boolean",
    "last_sync_at": "datetime"
  }
}
```

**接口需求**：
- **同步状态**：`GET /api/sync/status`
- **增量同步**：`POST /api/sync/delta`
- **冲突解决**：`POST /api/sync/resolve-conflict`
- **同步配置**：`GET/PUT /api/sync/config`
- **同步历史**：`GET /api/sync/history`

#### 🎨 交互设计
- **同步状态指示器**：实时显示同步状态（同步中、已同步、冲突等）
- **冲突解决界面**：当出现冲突时，提供直观的对比和选择界面
- **同步设置页面**：允许用户配置同步策略和范围
- **离线提示**：网络断开时显示离线状态和待同步数据数量

#### ✅ 验收标准与自动化测试

**功能验收标准**：
- [ ] **实时同步验收**：设备A的数据变更能在5秒内同步到设备B
- [ ] **冲突解决验收**：数据冲突能被正确检测并按策略解决
- [ ] **离线同步验收**：离线7天内的数据变更能在联网后正确同步
- [ ] **数据完整性验收**：同步过程中数据不丢失、不损坏

**异常流程验收**：
- [ ] **网络异常验收**：网络不稳定时能正确处理同步失败和重试
- [ ] **并发冲突验收**：多设备同时修改同一数据时冲突处理正确
- [ ] **大数据量验收**：支持10万+学习记录的高效同步

**性能要求验收**：
- [ ] **同步延迟验收**：正常网络下同步延迟 < 5秒
- [ ] **离线支持验收**：支持离线使用7天，数据缓存不丢失
- [ ] **并发处理验收**：支持1000+用户同时进行数据同步

---

## 6. 非功能需求

- **性能要求**：
  - **响应时间**：API接口响应时间 < 500ms，页面加载时间 < 2秒
  - **并发处理**：支持5000+并发用户，峰值时段10000+并发
  - **数据处理**：支持单用户10万+学习卡片，100万+学习记录
  - **算法性能**：FSRS算法计算时间 < 50ms，语音识别延迟 < 2秒
  - **同步性能**：多设备数据同步延迟 < 5秒，离线数据同步 < 30秒
  - **存储性能**：本地数据库查询响应时间 < 100ms，大文件上传 < 30秒

- **安全性要求**：
  - **身份认证**：JWT Token认证机制，Token有效期7天，支持自动续期
  - **数据传输**：所有网络通信使用HTTPS加密，API接口支持CORS
  - **数据存储**：敏感数据本地加密存储，用户密码使用bcrypt加密
  - **权限控制**：基于角色的访问控制，API接口权限验证
  - **防护机制**：防止SQL注入、XSS攻击、CSRF攻击等安全威胁
  - **隐私保护**：用户数据匿名化处理，不收集不必要的个人信息

- **兼容性要求**：
  - **移动平台**：支持iOS 12+和Android 8+，适配主流设备型号
  - **屏幕适配**：支持4.7-12.9英寸屏幕，响应式布局设计
  - **系统版本**：向后兼容至少2个主要版本，平滑升级机制
  - **网络环境**：支持2G/3G/4G/5G/WiFi网络，弱网络环境优化
  - **语言支持**：支持中文、英文界面，后续扩展多语言

- **可用性要求**：
  - **系统稳定性**：系统可用性 > 99.9%，故障恢复时间 < 5分钟
  - **离线支持**：核心功能完全离线可用，离线数据缓存7天
  - **容错机制**：网络异常、存储异常等情况下的优雅降级
  - **监控告警**：实时监控系统状态，异常情况自动告警
  - **备份恢复**：数据自动备份，支持一键恢复功能

- **用户体验要求**：
  - **界面设计**：遵循Material Design规范，界面简洁美观
  - **交互体验**：操作流程简化，关键功能3步内完成
  - **反馈机制**：操作结果及时反馈，加载状态清晰显示
  - **个性化**：支持主题切换、字体大小调节等个性化设置
  - **无障碍**：支持视觉障碍用户的无障碍访问功能

- **隐私及合规要求**：
  - **数据保护**：遵循GDPR、CCPA等数据保护法规
  - **用户权利**：用户数据可查看、修改、删除和导出
  - **数据最小化**：只收集必要的用户数据，定期清理过期数据
  - **透明度**：清晰的隐私政策和用户协议，数据使用透明化
  - **合规审计**：定期进行合规性审计，确保持续合规

---

## 7. 上线与验收

- **上线时间预期**：
  - **Alpha版本**：2025年3月，内部测试版本，核心功能完成
  - **Beta版本**：2025年4月，公开测试版本，功能基本完整
  - **正式版本**：2025年6月，正式发布版本，所有功能完成

- **整体验收标准**：
  - **功能完整性**：所有Must优先级功能100%完成，Should优先级功能80%完成
  - **性能指标**：所有性能要求达标，通过压力测试和性能测试
  - **质量标准**：Bug密度 < 1个/千行代码，严重Bug为0
  - **用户体验**：用户满意度 > 4.5分，核心流程用户完成率 > 90%
  - **安全合规**：通过安全测试，符合数据保护法规要求

- **分阶段验收计划**：
  - **第一阶段**（2025年3月）：核心功能验收
    - 用户认证系统、FSRS算法系统、多媒体卡片系统
    - 本地数据库存储、基础数据同步功能
  - **第二阶段**（2025年4月）：完整功能验收
    - 语音学习功能、知识管理系统、学习分析系统
    - 完整的数据同步机制、设备管理系统
  - **第三阶段**（2025年5月）：优化和测试
    - 考试备考模式、性能优化、安全加固
    - 用户体验优化、Bug修复、文档完善

- **灰度发布方案**：
  - **内测阶段**（2025年3月）：50名内部用户，收集基础功能反馈
  - **小范围测试**（2025年4月）：500名邀请用户，验证核心流程
  - **公开测试**（2025年5月）：5000名公开用户，压力测试和优化
  - **正式发布**（2025年6月）：全量发布，逐步开放所有功能

---

## 8. 附录

- **参考文档**：
  - **算法文档**：FSRS算法论文和实现规范
  - **技术规范**：Flutter开发规范、FastAPI最佳实践
  - **设计规范**：Material Design设计指南、无障碍设计规范
  - **安全标准**：OWASP安全开发指南、数据保护法规文档
  - **测试标准**：移动应用测试规范、性能测试标准

- **补充说明**：
  - 本文档基于现有需求文档整合，结合PRD模板规范编写
  - 详细技术实现方案参考技术设计文档（TDD）
  - 具体开发任务分解参考项目管理文档
  - 测试用例和验收标准参考测试计划文档
  - 用户界面设计参考UI/UX设计文档

- **术语表**：
  - **FSRS**：Free Spaced Repetition Scheduler，自由间隔重复调度算法
  - **SRS**：Spaced Repetition System，间隔重复系统
  - **DAO**：Data Access Object，数据访问对象
  - **JWT**：JSON Web Token，JSON网络令牌
  - **SQLite**：轻量级关系型数据库
  - **Sherpa-ONNX**：开源语音识别引擎

- **需求变更记录**：

| 版本 | 变更日期   | 变更内容                           | 影响模块     | 变更原因         |
| ---- | ---------- | ---------------------------------- | ------------ | ---------------- |
| 1.0  | 2025-01-19 | 初始版本创建，基于模板规范重构     | 全部模块     | 文档标准化       |
| 1.1  | 2025-01-22 | 完善功能模块设计，增加详细流程描述 | 所有功能模块 | 提高开发可执行性 |
| 1.2  | 2025-01-22 | 用户认证系统拆分为两个独立模块     | 4.1, 4.2模块 | 代码架构优化     |
