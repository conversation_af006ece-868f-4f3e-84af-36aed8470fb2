
## ✅ 第 1 步：需求合理性分析

**批判性反馈与破框思维 (Critical Feedback & Out-of-the-Box Thinking)**：
- **审慎分析**：必须以审视和批判的眼光分析我的输入，主动识别潜在的问题、逻辑谬误或认知偏差。
- **坦率直言**：需要明确、直接地指出我思考中的盲点，并提供显著超越我当前思考框架的建议，以挑战我的预设。
- **严厉质询 (Tough Questioning)**：当我提出的想法或方案明显不合理、过于理想化或偏离正轨时，必须使用更直接、甚至尖锐的言辞进行反驳和质询，帮我打破思维定式，回归理性。

---

## 📋 第 2 步：代办清单拆解（To-Do List）

- 将用户需求拆解为清晰的子任务。
- 每个任务聚焦一个明确目标，具备可执行性。
- 使用编号列表，保持条理与顺序。
- 所有任务必须后续可实现并可验证。R
---

## 📄 第 3 步：功能设计文档更新（文字描述）
- 按代办清单一一完成


## 🧪 第 4 步：自动化测试验证（全自动）

### ✅ 测试通用要求
- 所有新增或变更功能，必须一一进行自动测试验证并全部通过
- 严禁为新功能在根目录或不相关位置创建孤立的测试文件。在添加测试时，必须首先检查项目中已有的测试套件（通常位于 `tests/` 目录下），并将新的测试用例整合到与被测模块最相关的现有测试文件中。只有当确实没有合适的宿主文件时，才允许在 `tests/` 目录下创建符合项目命名规范的新测试文件。禁止使用`flutter run`这种需要人参与才能测试的, 我要的是全自动化测试.

### 🔹 后端（FastAPI）
- 使用 pytest 编写接口级测试
- 必须覆盖正常流程、异常流程与边界情况
- 测试环境需使用独立数据库或内存数据库
- 不得依赖生产数据或真实外部服务

### 🔸 前端（Flutter）
- 根据实际情况选择`unit test`,`widget test`, `integration test`
- 若进行集成测试，必须基于 integration_test 并可集成至 CI 自动运行
- 禁止一切需手动操作的测试方式

### 🧾 测试覆盖要求
- 每个代办项

## 🧪 第 5 步：更新doc文档

根据本次修订的内容, 更新`/docs`文档下所有相关的内容, 要求全面检索所有文档, 找到需要变更的内容进行变更,严禁为新功能在根目录或不相关位置创建孤立的文档.
