# CheeStack 技术架构设计

## 🏗️ 整体架构概览

CheeStack 采用**前后端分离 + 本地优先**的架构设计，确保用户数据安全和离线可用性。

```mermaid
graph TB
    subgraph "客户端层"
        A[Flutter App]
        B[本地SQLite数据库]
        C[FSRS算法引擎]
        D[Sherpa-ONNX语音引擎]
    end
    
    subgraph "网络层"
        E[HTTPS/WSS连接]
        F[数据同步服务]
    end
    
    subgraph "服务端层"
        G[FastAPI网关]
        H[认证服务]
        I[内容服务]
        J[学习服务]
        K[同步服务]
    end
    
    subgraph "数据层"
        L[PostgreSQL]
        M[Redis缓存]
        N[对象存储]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    E --> F
    F --> G
    G --> H
    G --> I
    G --> J
    G --> K
    H --> L
    I --> L
    J --> L
    K --> L
    G --> M
    I --> N
```

## 📱 前端架构 (Flutter)

### 技术栈选择
```yaml
框架: Flutter 3.16+
语言: Dart 3.0+
状态管理: GetX 4.6+
本地存储: sqflite 2.3+
网络请求: dio 5.3+
依赖注入: get_it 7.6+
路由管理: GetX路由
UI组件: Material Design 3
```

### 目录结构
```
lib/
├── core/                    # 核心基础设施
│   ├── di/                 # 依赖注入配置
│   ├── database/           # 本地数据库配置
│   ├── network/            # 网络客户端配置
│   ├── routing/            # 路由配置
│   ├── theme/              # 主题配置
│   └── constants/          # 常量定义
├── features/               # 功能模块
│   ├── auth/              # 认证模块
│   │   ├── data/          # 数据层
│   │   ├── domain/        # 业务层
│   │   └── presentation/  # 表现层
│   ├── content/           # 内容管理模块
│   ├── learning/          # 学习模块
│   └── sync/              # 同步模块
├── shared/                # 共享组件
│   ├── widgets/           # 通用UI组件
│   ├── utils/             # 工具函数
│   └── models/            # 共享数据模型
└── main.dart              # 应用入口
```

### 架构分层
```
┌─────────────────────────────────────┐
│         Presentation Layer          │  ← UI组件、页面、控制器
├─────────────────────────────────────┤
│         Application Layer           │  ← 业务用例、状态管理
├─────────────────────────────────────┤
│           Domain Layer              │  ← 实体、仓储接口、业务规则
├─────────────────────────────────────┤
│            Data Layer               │  ← 数据源、仓储实现、模型
├─────────────────────────────────────┤
│       Infrastructure Layer          │  ← 数据库、网络、外部服务
└─────────────────────────────────────┘
```

### 核心设计原则
1. **Clean Architecture**: 依赖倒置，业务逻辑与框架解耦
2. **SOLID原则**: 单一职责、开闭原则、依赖注入
3. **本地优先**: 所有功能优先使用本地数据，网络作为同步手段
4. **响应式设计**: 使用GetX实现响应式状态管理

## 🖥️ 后端架构 (FastAPI)

### 技术栈选择
```yaml
框架: FastAPI 0.104+
语言: Python 3.11+
ORM: Tortoise ORM 0.20+
数据库: PostgreSQL 15+
缓存: Redis 7.0+
认证: JWT + bcrypt
部署: Docker + Kubernetes
监控: Prometheus + Grafana
```

### 目录结构
```
cheestack-fastapi/
├── core/                   # 核心基础设施
│   ├── config.py          # 配置管理
│   ├── database.py        # 数据库配置
│   ├── security.py        # 安全相关
│   ├── middleware.py      # 中间件
│   └── exceptions.py      # 异常处理
├── apps/                  # 应用模块
│   ├── auth/             # 认证模块
│   │   ├── models.py     # 数据模型
│   │   ├── schemas.py    # API模式
│   │   ├── services.py   # 业务逻辑
│   │   └── routers.py    # 路由处理
│   ├── study/            # 学习模块
│   ├── general/          # 通用模块
│   └── sync/             # 同步模块
├── migrations/           # 数据库迁移
├── tests/               # 测试代码
└── main.py              # 应用入口
```

### 架构分层
```
┌─────────────────────────────────────┐
│         Presentation Layer          │  ← FastAPI路由、请求处理
├─────────────────────────────────────┤
│         Application Layer           │  ← 业务服务、用例实现
├─────────────────────────────────────┤
│           Domain Layer              │  ← 业务实体、领域服务
├─────────────────────────────────────┤
│       Infrastructure Layer          │  ← 数据库、缓存、外部服务
└─────────────────────────────────────┘
```

## 🗄️ 数据架构设计

### 本地数据库 (SQLite)
```sql
-- 核心表结构
users              -- 用户信息
user_configs       -- 用户配置
books              -- 学习书籍
chapters           -- 书籍章节
cards              -- 学习卡片
card_assets        -- 卡片资源
fsrs_cards         -- FSRS算法数据
study_records      -- 学习记录
sync_records       -- 同步记录
```

### 云端数据库 (PostgreSQL)
```sql
-- 分库分表策略
cheestack_main     -- 主库：用户、书籍、配置
├── users
├── books
├── chapters
└── user_configs

cheestack_study    -- 学习库：卡片、记录
├── cards
├── study_records
├── fsrs_cards
└── voice_records

cheestack_sync     -- 同步库：同步记录
├── sync_records
├── conflict_logs
└── device_info
```

### 数据同步策略
```
本地数据变更 → 生成同步记录 → 上传到云端 → 推送到其他设备
     ↑                                           ↓
离线缓存 ← 网络异常处理 ← 冲突检测解决 ← 接收云端推送
```

## 🔄 核心算法架构

### FSRS算法实现
```python
# 算法核心参数
w = [17个参数]  # FSRS模型参数
request_retention = 0.9  # 目标记忆保持率
maximum_interval = 36500  # 最大间隔天数

# 核心计算流程
def fsrs_calculate(card_state, rating, elapsed_days):
    """
    FSRS算法核心计算
    输入：卡片状态、用户评分、经过天数
    输出：新的稳定性、难度、下次复习间隔
    """
    stability = calculate_stability(card_state, rating)
    difficulty = calculate_difficulty(card_state, rating)
    interval = calculate_interval(stability, request_retention)
    return stability, difficulty, interval
```

### 语音识别架构 (Sherpa-ONNX)
```
音频输入 → 预处理 → 特征提取 → 模型推理 → 后处理 → 识别结果
   ↓         ↓         ↓         ↓         ↓         ↓
16kHz    降噪处理   MFCC特征   ONNX模型   CTC解码   文本输出
```

## 🔐 安全架构设计

### 认证授权流程
```
用户登录 → JWT Token生成 → Token验证 → 权限检查 → 资源访问
    ↓           ↓            ↓          ↓          ↓
密码验证    设备绑定      Token刷新    RBAC权限   API调用
```

### 数据安全策略
1. **传输安全**: HTTPS + TLS 1.3
2. **存储安全**: 本地数据AES加密
3. **访问控制**: JWT + RBAC权限模型
4. **隐私保护**: 数据最小化原则

## 📊 性能架构设计

### 性能指标要求
```yaml
响应时间:
  API接口: < 500ms
  页面加载: < 2s
  FSRS计算: < 50ms
  语音识别: < 2s

并发能力:
  同时在线用户: 10,000+
  API请求QPS: 1,000+
  数据库连接: 100+

存储容量:
  单用户数据: < 100MB
  媒体文件: < 1GB
  总存储: 可水平扩展
```

### 性能优化策略
1. **前端优化**: 懒加载、缓存、预加载
2. **后端优化**: 连接池、缓存、异步处理
3. **数据库优化**: 索引、分库分表、读写分离
4. **网络优化**: CDN、压缩、HTTP/2

## 🚀 部署架构设计

### 容器化部署
```yaml
# Docker容器组织
cheestack-frontend:  # Flutter Web版本
  image: nginx:alpine
  ports: ["80:80"]

cheestack-backend:   # FastAPI服务
  image: python:3.11-slim
  ports: ["8000:8000"]
  depends_on: [postgres, redis]

postgres:           # 主数据库
  image: postgres:15
  volumes: ["/data/postgres"]

redis:              # 缓存数据库
  image: redis:7-alpine
  volumes: ["/data/redis"]
```

### Kubernetes部署
```yaml
# K8s资源配置
Namespace: cheestack
Deployments: [backend, frontend]
Services: [backend-svc, frontend-svc]
ConfigMaps: [app-config]
Secrets: [app-secrets]
PersistentVolumes: [postgres-pv, redis-pv]
```

## 🔍 监控架构设计

### 监控指标体系
```yaml
应用指标:
  - 请求量、响应时间、错误率
  - 用户活跃度、功能使用率
  - 业务转化率、留存率

系统指标:
  - CPU、内存、磁盘、网络
  - 数据库连接数、查询性能
  - 缓存命中率、队列长度

业务指标:
  - 学习记录数、同步成功率
  - 算法计算性能、准确率
  - 用户满意度、反馈数量
```

---

**注意**：本架构设计是整个系统的技术蓝图，所有模块的具体实现都应该遵循这里定义的架构原则和技术选型。
