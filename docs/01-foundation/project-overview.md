# CheeStack 项目概述

## 🎯 项目定位

CheeStack（芝士堆）是一款基于FSRS算法的智能学习工具，专注于帮助用户高效记忆和掌握各类知识点。通过科学的间隔重复算法，结合多媒体学习内容和智能复习调度，为用户提供个性化的学习体验。

## 📊 核心价值主张

### 用户价值
- **学习效率提升**：基于FSRS算法的科学复习调度，显著提升记忆保持率
- **个性化体验**：根据用户学习表现动态调整学习策略
- **多媒体支持**：支持文字、图片、音频、视频等多种学习内容
- **离线优先**：完全离线可用，数据本地存储，网络恢复后自动同步

### 技术价值
- **算法先进性**：采用最新的FSRS算法，相比传统SM算法有显著优势
- **架构现代化**：前后端分离，微服务架构，支持水平扩展
- **跨平台支持**：Flutter前端，支持iOS、Android、Web多平台
- **数据安全**：本地优先存储，端到端加密，用户数据完全可控

## 🏗️ 系统架构概览

### 技术栈选择
```
前端：Flutter 3.16+ (Dart)
├── 状态管理：GetX
├── 本地存储：SQLite (sqflite)
├── 网络请求：Dio
└── UI框架：Material Design 3

后端：FastAPI (Python 3.11+)
├── ORM：Tortoise ORM
├── 数据库：PostgreSQL
├── 缓存：Redis
├── 认证：JWT
└── 部署：Docker + K8s

算法核心：FSRS算法 (Python/Dart双实现)
├── 记忆模型：17参数FSRS模型
├── 个性化：用户历史数据优化
└── 性能：本地计算，毫秒级响应
```

### 部署架构
```
用户设备 (Flutter App)
    ↓ HTTPS/WSS
负载均衡器 (Nginx)
    ↓
API网关 (FastAPI)
    ↓
微服务集群
├── 认证服务
├── 内容服务
├── 学习服务
└── 同步服务
    ↓
数据层
├── PostgreSQL (主数据)
├── Redis (缓存)
└── 对象存储 (媒体文件)
```

## 👥 目标用户群体

### 主要用户画像
1. **语言学习者** (35%)
   - 年龄：18-35岁
   - 需求：词汇记忆、语法学习、口语练习
   - 使用频率：每天30-60分钟

2. **学生群体** (40%)
   - 年龄：12-25岁
   - 需求：考试备考、知识点记忆、错题复习
   - 使用频率：每天60-120分钟

3. **职场人士** (20%)
   - 年龄：25-40岁
   - 需求：技能提升、证书考试、专业知识
   - 使用频率：每天20-40分钟

4. **知识爱好者** (5%)
   - 年龄：25-50岁
   - 需求：兴趣学习、知识积累、自我提升
   - 使用频率：每天15-30分钟

## 🎯 核心功能模块

### 必需功能 (Must Have)
1. **用户认证系统**
   - 一键登录、密码登录、生物识别
   - JWT认证、多设备管理
   - 用户资料管理

2. **内容创作系统**
   - 书籍章节管理
   - 多媒体卡片创作
   - 标签分类系统

3. **FSRS学习算法**
   - 智能复习调度
   - 个性化参数优化
   - 学习记录跟踪

4. **本地数据存储**
   - SQLite本地数据库
   - 离线优先设计
   - 数据完整性保证

5. **多设备同步**
   - 增量数据同步
   - 冲突检测解决
   - 离线数据缓存

### 重要功能 (Should Have)
1. **语音学习功能**
   - 本地语音识别 (Sherpa-ONNX)
   - 发音评估打分
   - 多语言支持

2. **考试备考模式**
   - 模拟考试系统
   - 成绩分析报告
   - 错题集管理

3. **学习分析系统**
   - 数据可视化分析
   - 学习报告生成
   - 个性化建议

### 期望功能 (Could Have)
1. **社区分享功能**
2. **AI智能助手**
3. **学习计划制定**
4. **成就系统**

## 📈 项目里程碑

### Phase 1: 核心功能 (3个月)
- 用户认证系统
- 基础内容创作
- FSRS算法实现
- 本地数据存储

### Phase 2: 完整功能 (2个月)
- 多设备同步
- 语音学习功能
- 学习分析系统
- 性能优化

### Phase 3: 增强功能 (1个月)
- 考试备考模式
- UI/UX优化
- 安全加固
- 部署上线

## 🔍 竞品分析

### 主要竞争对手
1. **Anki**
   - 优势：功能强大、社区活跃
   - 劣势：界面复杂、算法陈旧

2. **Quizlet**
   - 优势：界面友好、内容丰富
   - 劣势：算法简单、个性化不足

3. **百词斩**
   - 优势：本土化好、营销强
   - 劣势：功能单一、算法落后

### 差异化优势
- **算法先进**：FSRS算法相比SM-2有显著优势
- **离线优先**：完全离线可用，数据安全可控
- **多媒体支持**：丰富的内容创作能力
- **个性化强**：基于用户数据的深度个性化

## 📋 成功指标

### 用户指标
- 日活跃用户数 (DAU)
- 用户留存率 (7日、30日)
- 用户学习时长
- 用户满意度评分

### 业务指标
- 用户增长率
- 付费转化率
- 客户生命周期价值 (LTV)
- 获客成本 (CAC)

### 技术指标
- 系统可用性 (>99.9%)
- API响应时间 (<500ms)
- 错误率 (<0.1%)
- 数据同步成功率 (>99.5%)

---

**注意**：本文档定义了项目的整体方向和目标，是所有后续技术决策的基础。任何重大变更都应该在这里体现并向下传播到其他文档。
